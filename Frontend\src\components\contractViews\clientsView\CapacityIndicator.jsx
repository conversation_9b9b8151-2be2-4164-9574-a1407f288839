export default function CapacityIndicator({ capacity }) {
  const { filled, max, available } = capacity;
  
  const getProgressColor = () => {
    if (max === 0) return 'bg-gray-300';
    const percentage = (filled / max) * 100;
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const getProgressPercentage = () => {
    if (max === 0) return 0;
    return Math.min((filled / max) * 100, 100);
  };

  return (
    <div className="bg-white rounded-md shadow-sm overflow-hidden">
      <div className="px-4 py-3 bg-white border-b border-black/[0.1]">
        <h3 className="font-medium text-gray-700">Capacity Overview</h3>
      </div>
      
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="text-2xl font-bold text-gray-900">
            {filled} of {max} seats filled
          </div>
          <div className="text-lg text-gray-600">
            ({available} available)
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
          <div 
            className={`h-3 rounded-full transition-all duration-300 ${getProgressColor()}`}
            style={{ width: `${getProgressPercentage()}%` }}
          ></div>
        </div>
        
        {/* Status Indicators */}
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
              <span className="text-gray-600">Available</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
              <span className="text-gray-600">Nearly Full</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
              <span className="text-gray-600">Full</span>
            </div>
          </div>
          
          {max > 0 && (
            <div className="text-gray-500">
              {Math.round(getProgressPercentage())}% capacity
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
