// /controllers/contactController.js
const { PrismaClient } = require('../generated/prisma/client');
const prisma = new PrismaClient();

// Add a new contact for a student
const addNewContact = async (req, res) => {
  try {
    const { studentId } = req.params;
    const {
      title,
      firstname,
      surname,
      knownAs,
      relationship,
      dateOfBirth,
      isPrimary = false,
      isPayer = false,
      contactMethods = [], // Array of { methodType, value }
      address
    } = req.body;

    // Validate student ID
    if (!studentId || isNaN(parseInt(studentId))) {
      return res.status(400).json({ message: 'Invalid student ID' });
    }

    // Validate required fields
    if (!firstname || !surname) {
      return res.status(400).json({ message: 'First name and surname are required' });
    }

    // Check if student exists
    const student = await prisma.student.findUnique({
      where: { id: parseInt(studentId) }
    });

    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    // If this is set as primary, unset other primary contacts
    if (isPrimary) {
      await prisma.studentContact.updateMany({
        where: {
          studentId: parseInt(studentId),
          isPrimary: true
        },
        data: {
          isPrimary: false
        }
      });
    }

    // Create the contact with transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the contact
      const newContact = await tx.studentContact.create({
        data: {
          studentId: parseInt(studentId),
          title,
          firstname,
          surname,
          knownAs,
          relationship,
          dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : null,
          isPrimary,
          isPayer
        }
      });

      // Add contact methods if provided
      if (contactMethods && contactMethods.length > 0) {
        await tx.studentContactMethod.createMany({
          data: contactMethods.map(method => ({
            contactId: newContact.id,
            methodType: method.methodType,
            value: method.value
          }))
        });
      }

      // Add address if provided
      if (address) {
        await tx.studentContactAddress.create({
          data: {
            contactId: newContact.id,
            addressLine1: address.addressLine1,
            addressLine2: address.addressLine2,
            city: address.city,
            county: address.county,
            postcode: address.postcode,
            validFrom: new Date(),
            isPrimary: address.isPrimary || false,
            updatedBy: req.user?.id // From auth middleware
          }
        });
      }

      return newContact;
    });

    // Fetch the complete contact data to return
    const contactWithRelations = await prisma.studentContact.findUnique({
      where: { id: result.id },
      include: {
        contactMethods: true,
        addresses: true
      }
    });

    res.status(201).json(contactWithRelations);
  } catch (error) {
    console.error('Error adding new contact:', error);
    res.status(500).json({ message: 'Failed to add new contact' });
  }
};

// Add a new note for a contact
const addNewNote = async (req, res) => {
  try {
    const { contactId } = req.params;
    const { content, noteType } = req.body;
    const authorId = req.user?.id; // From auth middleware

    // Validate contact ID
    if (!contactId || isNaN(parseInt(contactId))) {
      return res.status(400).json({ message: 'Invalid contact ID' });
    }

    // Validate required fields
    if (!content || !content.trim()) {
      return res.status(400).json({ message: 'Note content is required' });
    }

    if (!authorId) {
      return res.status(401).json({ message: 'User authentication required' });
    }

    // Check if contact exists
    const contact = await prisma.studentContact.findUnique({
      where: { id: parseInt(contactId) }
    });

    if (!contact) {
      return res.status(404).json({ message: 'Contact not found' });
    }

    // Create the note
    const newNote = await prisma.studentContactNote.create({
      data: {
        contactId: parseInt(contactId),
        authorId: authorId,
        content: content.trim(),
        noteType: noteType || null
      },
      include: {
        author: {
          select: {
            id: true,
            firstName: true,
            surname: true
          }
        }
      }
    });

    res.status(201).json(newNote);
  } catch (error) {
    console.error('Error adding new note:', error);
    res.status(500).json({ message: 'Failed to add new note' });
  }
};

// Add a new communication for a contact
const addNewCommunication = async (req, res) => {
  try {
    const { contactId } = req.params;
    const {
      commType,
      commGroup,
      subject,
      content,
      sentAt
    } = req.body;
    const authorId = req.user?.id; // From auth middleware

    // Validate contact ID
    if (!contactId || isNaN(parseInt(contactId))) {
      return res.status(400).json({ message: 'Invalid contact ID' });
    }

    // Validate required fields
    if (!commType || !subject || !content) {
      return res.status(400).json({ message: 'Communication type, subject, and content are required' });
    }

    if (!authorId) {
      return res.status(401).json({ message: 'User authentication required' });
    }

    // Get contact with student info
    const contact = await prisma.studentContact.findUnique({
      where: { id: parseInt(contactId) },
      include: {
        student: true
      }
    });

    if (!contact) {
      return res.status(404).json({ message: 'Contact not found' });
    }

    // Create the communication
    const newCommunication = await prisma.studentContactCommunication.create({
      data: {
        studentId: contact.studentId,
        contactId: parseInt(contactId),
        authorId: authorId,
        commType,
        commGroup: commGroup || 'general',
        subject,
        content,
        sentAt: sentAt ? new Date(sentAt) : new Date()
      },
      include: {
        author: {
          select: {
            id: true,
            firstName: true,
            surname: true
          }
        },
        student: {
          select: {
            id: true,
            firstName: true,
            surname: true
          }
        },
        contact: {
          select: {
            id: true,
            firstname: true,
            surname: true
          }
        }
      }
    });

    res.status(201).json(newCommunication);
  } catch (error) {
    console.error('Error adding new communication:', error);
    res.status(500).json({ message: 'Failed to add new communication' });
  }
};

const updateBasicInfo = async (req, res) => {
  try {
    const { contactId } = req.params;
    const {
      title,
      firstname,
      surname,
      knownAs,
      relationship,
      dateOfBirth,
      isPrimary,
      isPayer,
      email,
      phone
    } = req.body;

    // Validate contact ID
    if (!contactId || isNaN(parseInt(contactId))) {
      return res.status(400).json({ message: 'Invalid contact ID' });
    }
    const cid = parseInt(contactId, 10);

    // Fetch existing contact (including methods) to know what’s there already
    const existingContact = await prisma.studentContact.findUnique({
      where: { id: cid },
      include: { contactMethods: true }
    });

    if (!existingContact) {
      return res.status(404).json({ message: 'Contact not found' });
    }

    // If we're setting this as primary (and it wasn’t primary before), unset others
    if (isPrimary === true && existingContact.isPrimary === false) {
      await prisma.studentContact.updateMany({
        where: {
          studentId: existingContact.studentId,
          isPrimary: true,
          id: { not: cid }
        },
        data: { isPrimary: false }
      });
    }

    // Build the update payload for the contact row itself
    const updateData = {};
    if (title !== undefined)        updateData.title = title;
    if (firstname !== undefined)    updateData.firstname = firstname;
    if (surname !== undefined)      updateData.surname = surname;
    if (knownAs !== undefined)      updateData.knownAs = knownAs;
    if (relationship !== undefined) updateData.relationship = relationship;
    if (dateOfBirth !== undefined) {
      updateData.dateOfBirth = dateOfBirth ? new Date(dateOfBirth) : null;
    }
    if (isPrimary !== undefined)    updateData.isPrimary = isPrimary;
    if (isPayer !== undefined)      updateData.isPayer = isPayer;

    // Prepare an array to collect all Prisma ops (for a single transaction)
    const txOps = [];

    // 1) Update the StudentContact row
    txOps.push(
      prisma.studentContact.update({
        where: { id: cid },
        data: updateData
      })
    );

    // ==== EMAIL logic (no deletes) ====
    if (email !== undefined) {
      // If email is provided (even an empty string), see if there's already an EMAIL method.
      const existingEmailMethod = existingContact.contactMethods.find(
        cm => cm.methodType === 'EMAIL'
      );

      if (existingEmailMethod) {
        // Always update to whatever string was passed (could be empty if you really want to clear it)
        txOps.push(
          prisma.studentContactMethod.update({
            where: { id: existingEmailMethod.id },
            data: { value: email }
          })
        );
      } else {
        // Create a new EMAIL method row, even if email is an empty string
        txOps.push(
          prisma.studentContactMethod.create({
            data: {
              contactId: cid,
              methodType: 'EMAIL',
              value: email
            }
          })
        );
      }
    }

    // ==== PHONE logic (unchanged) ====
    if (phone !== undefined) {
      const existingPhoneMethod = existingContact.contactMethods.find(
        cm => cm.methodType === 'PHONE'
      );

      if (phone === null || phone === '') {
        // If you do want to delete phone when it’s explicitly null/empty, keep this:
        txOps.push(
          prisma.studentContactMethod.deleteMany({
            where: {
              contactId: cid,
              methodType: 'PHONE'
            }
          })
        );
      } else {
        // Otherwise update or create
        if (existingPhoneMethod) {
          txOps.push(
            prisma.studentContactMethod.update({
              where: { id: existingPhoneMethod.id },
              data: { value: phone }
            })
          );
        } else {
          txOps.push(
            prisma.studentContactMethod.create({
              data: {
                contactId: cid,
                methodType: 'PHONE',
                value: phone
              }
            })
          );
        }
      }
    }

    // Run all ops in one transaction
    await prisma.$transaction(txOps);

    // Fetch and return the final state (including methods)
    const finalContact = await prisma.studentContact.findUnique({
      where: { id: cid },
      include: { contactMethods: true }
    });

    return res.status(200).json(finalContact);
  } catch (error) {
    console.error('Error updating contact basic info:', error);
    return res.status(500).json({
      message: 'Failed to update contact basic information'
    });
  }
};

// Delete a contact by ID
const deleteContact = async (req, res) => {
  const { contactId } = req.params;

  try {
    // Validate contactId
    if (!contactId || isNaN(parseInt(contactId))) {
      return res.status(400).json({
        success: false,
        message: 'Invalid contact ID provided'
      });
    }

    const contactIdInt = parseInt(contactId);

    // Check if contact exists
    const existingContact = await prisma.studentContact.findUnique({
      where: { id: contactIdInt },
      include: {
        contactMethods: true,
        addresses: true,
        notes: true,
        communications: true
      }
    });

    if (!existingContact) {
      return res.status(404).json({
        success: false,
        message: 'Contact not found'
      });
    }

    // Check if this is the primary contact and if there are other contacts
    if (existingContact.isPrimary) {
      const otherContacts = await prisma.studentContact.findMany({
        where: {
          studentId: existingContact.studentId,
          id: { not: contactIdInt }
        }
      });

      // If there are other contacts, we should warn or prevent deletion
      // Or automatically assign primary status to another contact
      if (otherContacts.length > 0) {
        // Option 1: Prevent deletion of primary contact if others exist
        // return res.status(400).json({
        //   success: false,
        //   message: 'Cannot delete primary contact while other contacts exist. Please assign primary status to another contact first.'
        // });

        // Option 2: Automatically assign primary to the first other contact
        await prisma.studentContact.update({
          where: { id: otherContacts[0].id },
          data: { isPrimary: true }
        });
      }
    }

    // Use transaction to delete all related data
    await prisma.$transaction(async (tx) => {
      // Delete related contact methods
      await tx.studentContactMethod.deleteMany({
        where: { contactId: contactIdInt }
      });

      // Delete related addresses
      await tx.studentContactAddress.deleteMany({
        where: { contactId: contactIdInt }
      });

      // Delete related notes
      await tx.studentContactNote.deleteMany({
        where: { contactId: contactIdInt }
      });

      // Delete related communications
      await tx.studentContactCommunication.deleteMany({
        where: { contactId: contactIdInt }
      });

      // Finally, delete the contact
      await tx.studentContact.delete({
        where: { id: contactIdInt }
      });
    });

    res.status(200).json({
      success: true,
      message: 'Contact deleted successfully',
      data: {
        deletedContactId: contactIdInt,
        deletedContact: {
          name: `${existingContact.firstname} ${existingContact.surname}`,
          relationship: existingContact.relationship
        }
      }
    });

  } catch (error) {
    console.error('Error deleting contact:', error);
    
    // Handle specific Prisma errors
    if (error.code === 'P2025') {
      return res.status(404).json({
        success: false,
        message: 'Contact not found'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error while deleting contact',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get contact by ID
const getContactById = async (req, res) => {
  const { contactId } = req.params;

  try {
    // Validate contactId
    if (!contactId || isNaN(parseInt(contactId))) {
      return res.status(400).json({
        success: false,
        message: 'Invalid contact ID provided'
      });
    }

    const contactIdInt = parseInt(contactId);

    // Fetch the contact with related data
    const contact = await prisma.studentContact.findUnique({
      where: { id: contactIdInt },
      include: {
        contactMethods: true,
      }
    });

    if (!contact) {
      return res.status(404).json({
        success: false,
        message: 'Contact not found'
      });
    }

    res.status(200).json({
      success: true,
      data: contact
    });

  } catch (error) {
    console.error('Error fetching contact:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while fetching contact',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  addNewContact,
  addNewNote,
  addNewCommunication,
  updateBasicInfo,
  deleteContact,
  getContactById
};
