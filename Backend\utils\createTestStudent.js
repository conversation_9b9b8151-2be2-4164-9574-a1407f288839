const { PrismaClient } = require('../generated/prisma/client');
const prisma = new PrismaClient();

async function createTestStudent() {
  console.log('Creating test student with correct budget code...');

  try {
    // Create a new student
    const student = await prisma.student.create({
      data: {
        title: 'Miss',
        firstName: '<PERSON>',
        surname: '<PERSON>',
        knownAs: '<PERSON>',
        gender: 'F',
        dateOfBirth: new Date('2011-03-10'),
        clientTypeId: 1, // HTS
        requiresTravel: true,
        inCare: false,
        yearGroupId: 1, // YR_7
        estabId: 1,
        isActive: true
      }
    });

    console.log(`Created student: ${student.firstName} ${student.surname} (ID: ${student.id})`);

    // Create code assignment with ELA001 budget code (matches contract 1)
    await prisma.studentCodeAssignment.create({
      data: {
        studentId: student.id,
        eligibilityCode: 'E001', // Statutory Distance
        budgetCode: 'ELA001', // Core Statutory Transport - matches contract 1
        fundingCode: 'LA_HTS', // LA Funded Home-to-School
        validFrom: new Date('2024-09-01'),
        validTo: null,
        createdBy: 1
      }
    });

    console.log(`Created code assignment for ${student.firstName} with budget code ELA001`);

    // Create another student
    const student2 = await prisma.student.create({
      data: {
        title: 'Mr',
        firstName: 'Michael',
        surname: 'Brown',
        knownAs: 'Mike',
        gender: 'M',
        dateOfBirth: new Date('2010-07-15'),
        clientTypeId: 1, // HTS
        requiresTravel: true,
        inCare: false,
        yearGroupId: 2, // YR_8
        estabId: 1,
        isActive: true
      }
    });

    console.log(`Created student: ${student2.firstName} ${student2.surname} (ID: ${student2.id})`);

    // Create code assignment with ELA001 budget code
    await prisma.studentCodeAssignment.create({
      data: {
        studentId: student2.id,
        eligibilityCode: 'E001', // Statutory Distance
        budgetCode: 'ELA001', // Core Statutory Transport - matches contract 1
        fundingCode: 'LA_HTS', // LA Funded Home-to-School
        validFrom: new Date('2024-09-01'),
        validTo: null,
        createdBy: 1
      }
    });

    console.log(`Created code assignment for ${student2.firstName} with budget code ELA001`);

    console.log('✅ Test students created successfully!');
  } catch (error) {
    console.error('Error creating test students:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

createTestStudent()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });
