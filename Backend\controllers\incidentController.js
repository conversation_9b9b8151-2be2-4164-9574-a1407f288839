// /controllers/incidentController.js
const { PrismaClient } = require('../generated/prisma/client');
const prisma = new PrismaClient();

// Get incidents for a contract
const getContractIncidents = async (req, res) => {
  try {
    const { contractId } = req.params;
    const { page = 1, limit = 10 } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const incidents = await prisma.contractIncident.findMany({
      where: {
        contractId: parseInt(contractId)
      },
      include: {
        incidentType: true,
        reporter: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      },
      orderBy: {
        occurredAt: 'desc'
      },
      skip,
      take: parseInt(limit)
    });

    const totalIncidents = await prisma.contractIncident.count({
      where: {
        contractId: parseInt(contractId)
      }
    });

    const formattedIncidents = incidents.map(incident => ({
      id: incident.id,
      title: incident.title,
      description: incident.description,
      severity: incident.severity,
      occurredAt: incident.occurredAt,
      createdAt: incident.createdAt,
      incidentType: {
        id: incident.incidentType.id,
        code: incident.incidentType.code,
        name: incident.incidentType.name
      },
      reporter: {
        id: incident.reporter.id,
        name: `${incident.reporter.firstName} ${incident.reporter.lastName}`
      }
    }));

    res.json({
      success: true,
      data: formattedIncidents,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalIncidents,
        totalPages: Math.ceil(totalIncidents / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Error fetching contract incidents:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch contract incidents'
    });
  }
};

// Get incident analytics for a contract
const getIncidentAnalytics = async (req, res) => {
  try {
    const { contractId } = req.params;

    const analytics = await prisma.contractIncident.groupBy({
      by: ['incidentTypeId'],
      where: {
        contractId: parseInt(contractId)
      },
      _count: {
        id: true
      }
    });

    const incidentTypes = await prisma.incidentType.findMany();
    
    const totalIncidents = analytics.reduce((sum, item) => sum + item._count.id, 0);

    const formattedAnalytics = analytics.map(item => {
      const incidentType = incidentTypes.find(type => type.id === item.incidentTypeId);
      const percentage = totalIncidents > 0 ? Math.round((item._count.id / totalIncidents) * 100) : 0;
      
      return {
        incidentType: {
          id: incidentType.id,
          code: incidentType.code,
          name: incidentType.name
        },
        count: item._count.id,
        percentage
      };
    });

    res.json({
      success: true,
      data: {
        analytics: formattedAnalytics,
        totalIncidents
      }
    });
  } catch (error) {
    console.error('Error fetching incident analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch incident analytics'
    });
  }
};

// Create new incident
const createIncident = async (req, res) => {
  try {
    const { contractId } = req.params;
    const { incidentTypeId, severity, title, description, occurredAt } = req.body;
    const reportedBy = req.staff.id; // From auth middleware

    const incident = await prisma.contractIncident.create({
      data: {
        contractId: parseInt(contractId),
        incidentTypeId: parseInt(incidentTypeId),
        severity,
        title,
        description,
        occurredAt: new Date(occurredAt),
        reportedBy
      },
      include: {
        incidentType: true,
        reporter: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      }
    });

    const formattedIncident = {
      id: incident.id,
      title: incident.title,
      description: incident.description,
      severity: incident.severity,
      occurredAt: incident.occurredAt,
      createdAt: incident.createdAt,
      incidentType: {
        id: incident.incidentType.id,
        code: incident.incidentType.code,
        name: incident.incidentType.name
      },
      reporter: {
        id: incident.reporter.id,
        name: `${incident.reporter.firstName} ${incident.reporter.lastName}`
      }
    };

    res.status(201).json({
      success: true,
      data: formattedIncident,
      message: 'Incident reported successfully'
    });
  } catch (error) {
    console.error('Error creating incident:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create incident'
    });
  }
};

// Get incident types
const getIncidentTypes = async (req, res) => {
  try {
    console.log('getIncidentTypes called');
    const incidentTypes = await prisma.incidentType.findMany({
      orderBy: {
        name: 'asc'
      }
    });

    console.log('Found incident types:', incidentTypes.length, incidentTypes);
    res.json({
      success: true,
      data: incidentTypes
    });
  } catch (error) {
    console.error('Error fetching incident types:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch incident types'
    });
  }
};

module.exports = {
  getContractIncidents,
  getIncidentAnalytics,
  createIncident,
  getIncidentTypes
};
