const { PrismaClient } = require('../generated/prisma/client');
const prisma = new PrismaClient();

// Get medical information for a student
const getMedicalByStudentId = async (req, res) => {
  try {
    const { studentId } = req.params;
    const studentIdInt = parseInt(studentId);

    if (isNaN(studentIdInt)) {
      return res.status(400).json({ 
        success: false, 
        message: 'Invalid student ID' 
      });
    }

    // Check if student exists
    const student = await prisma.student.findUnique({
      where: { id: studentIdInt },
      include: {
        establishment: true,
        yearGroup: true
      }
    });

    if (!student) {
      return res.status(404).json({ 
        success: false, 
        message: 'Student not found' 
      });
    }

    // Get the most recent medical record for the student
    const medicalRecord = await prisma.studentsMedical.findFirst({
      where: { studentId: studentIdInt },
      include: {
        conditions: true,
        equipment: true,
        trainingTags: true,
        createdByStaff: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        },
        updatedByStaff: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      },
      orderBy: { updatedAt: 'desc' }
    });

    // Check if medical alert should be shown
    const hasHighRiskAlert = medicalRecord && (
      medicalRecord.severity === 'High' ||
      medicalRecord.conditions.some(condition => 
        ['Epilepsy', 'Severe Asthma', 'Seizure Risk'].includes(condition.condition)
      )
    );

    res.json({
      success: true,
      data: {
        student: {
          id: student.id,
          firstName: student.firstName,
          surname: student.surname,
          knownAs: student.knownAs,
          dateOfBirth: student.dateOfBirth,
          establishment: student.establishment,
          yearGroup: student.yearGroup
        },
        medicalRecord,
        hasHighRiskAlert
      }
    });

  } catch (error) {
    console.error('Error fetching medical data:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
};

// Update student's medical information
const updateStudentMedical = async (req, res) => {
  try {
    const { studentId } = req.params;
    const studentIdInt = parseInt(studentId);
    const staffId = req.staff.id; // From auth middleware

    if (isNaN(studentIdInt)) {
      return res.status(400).json({ 
        success: false, 
        message: 'Invalid student ID' 
      });
    }

    const {
      severity,
      requiresPa,
      assignedPaName,
      assignedPaPhone,
      riskLevel,
      travelAloneAlert,
      violenceAlert,
      notes,
      conditions = [],
      equipment = [],
      trainingTags = []
    } = req.body;

    // Check if student exists
    const student = await prisma.student.findUnique({
      where: { id: studentIdInt }
    });

    if (!student) {
      return res.status(404).json({ 
        success: false, 
        message: 'Student not found' 
      });
    }

    // Start transaction
    const result = await prisma.$transaction(async (tx) => {
      // Get existing medical record
      const existingRecord = await tx.studentsMedical.findFirst({
        where: { studentId: studentIdInt },
        orderBy: { updatedAt: 'desc' }
      });

      let medicalRecord;

      if (existingRecord) {
        // Update existing record
        medicalRecord = await tx.studentsMedical.update({
          where: { id: existingRecord.id },
          data: {
            severity,
            requiresPa,
            assignedPaName,
            assignedPaPhone,
            riskLevel,
            travelAloneAlert,
            violenceAlert,
            notes,
            updatedBy: staffId,
            updatedAt: new Date()
          }
        });

        // Delete existing related records
        await tx.studentsMedicalConditions.deleteMany({
          where: { medicalId: existingRecord.id }
        });
        await tx.studentsMedicalEquipment.deleteMany({
          where: { medicalId: existingRecord.id }
        });
        await tx.studentsMedicalTrainingTags.deleteMany({
          where: { medicalId: existingRecord.id }
        });
      } else {
        // Create new record
        medicalRecord = await tx.studentsMedical.create({
          data: {
            studentId: studentIdInt,
            severity,
            requiresPa,
            assignedPaName,
            assignedPaPhone,
            riskLevel,
            travelAloneAlert,
            violenceAlert,
            notes,
            createdBy: staffId,
            updatedBy: staffId
          }
        });
      }

      // Insert conditions
      if (conditions.length > 0) {
        await tx.studentsMedicalConditions.createMany({
          data: conditions.map(condition => ({
            medicalId: medicalRecord.id,
            condition: condition.condition,
            details: condition.details || null
          }))
        });
      }

      // Insert equipment
      if (equipment.length > 0) {
        await tx.studentsMedicalEquipment.createMany({
          data: equipment.map(item => ({
            medicalId: medicalRecord.id,
            equipmentType: item.equipmentType,
            notes: item.notes || null
          }))
        });
      }

      // Insert training tags - Fix: trainingTags array contains strings, not objects
      if (trainingTags.length > 0) {
        await tx.studentsMedicalTrainingTags.createMany({
          data: trainingTags.map(tag => ({
            medicalId: medicalRecord.id,
            tag: typeof tag === 'string' ? tag : tag.tag // Handle both string and object formats
          }))
        });
      }

      return medicalRecord;
    });

    // Fetch the complete updated record
    const updatedRecord = await prisma.studentsMedical.findUnique({
      where: { id: result.id },
      include: {
        conditions: true,
        equipment: true,
        trainingTags: true,
        createdByStaff: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        },
        updatedByStaff: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      }
    });

    res.json({
      success: true,
      message: 'Medical information updated successfully',
      data: updatedRecord
    });

  } catch (error) {
    console.error('Error updating medical data:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
};

module.exports = {
  getMedicalByStudentId,
  updateStudentMedical
};