const { PrismaClient } = require('../generated/prisma/client');
const prisma = new PrismaClient();

// Get all budget codes with their current rates
const getBudgetCodes = async (req, res) => {
  try {
    const budgetCodes = await prisma.budgetCode.findMany({
      include: {
        rates: {
          where: {
            OR: [
              { validTo: null },
              { validTo: { gte: new Date() } }
            ]
          },
          orderBy: { validFrom: 'desc' },
          take: 1
        }
      },
      orderBy: { code: 'asc' }
    });

    const formattedCodes = budgetCodes.map(code => ({
      code: code.code,
      name: code.name,
      description: code.description,
      currentRate: code.rates[0]?.rate || null,
      validFrom: code.rates[0]?.validFrom || null,
      validTo: code.rates[0]?.validTo || null
    }));

    res.json({
      success: true,
      data: formattedCodes
    });
  } catch (error) {
    console.error('Error fetching budget codes:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch budget codes'
    });
  }
};

// Get budget code rate history
const getBudgetCodeHistory = async (req, res) => {
  try {
    const rateHistory = await prisma.budgetCodeRate.findMany({
      include: {
        budgetCode: true,
        createdByStaff: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      },
      orderBy: [
        { code: 'asc' },
        { validFrom: 'desc' }
      ]
    });

    res.json({
      success: true,
      data: rateHistory
    });
  } catch (error) {
    console.error('Error fetching budget code history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch budget code history'
    });
  }
};

// Update budget code rate
const updateBudgetCodeRate = async (req, res) => {
  try {
    const { code } = req.params;
    const { rate, reason, validFrom } = req.body;
    const staffId = req.user?.id || 1; // Default to admin for now

    // Close the current rate if it exists
    const currentRate = await prisma.budgetCodeRate.findFirst({
      where: {
        code: code,
        OR: [
          { validTo: null },
          { validTo: { gte: new Date() } }
        ]
      }
    });

    if (currentRate) {
      await prisma.budgetCodeRate.update({
        where: { rateId: currentRate.rateId },
        data: {
          validTo: new Date(validFrom),
          updatedBy: staffId,
          updatedAt: new Date()
        }
      });
    }

    // Create new rate
    const newRate = await prisma.budgetCodeRate.create({
      data: {
        code: code,
        rate: parseFloat(rate),
        reason: reason || null,
        validFrom: new Date(validFrom),
        validTo: null,
        createdBy: staffId
      },
      include: {
        budgetCode: true,
        createdByStaff: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      }
    });

    res.json({
      success: true,
      data: newRate
    });
  } catch (error) {
    console.error('Error updating budget code rate:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update budget code rate'
    });
  }
};

// Get contract budget assignment
const getContractBudgetAssignment = async (req, res) => {
  try {
    const { contractId } = req.params;

    // Get current assignment
    const currentAssignment = await prisma.operatorContractBudgetAssignment.findFirst({
      where: {
        contractId: parseInt(contractId),
        validFrom: { lte: new Date() },
        OR: [
          { validTo: null },
          { validTo: { gte: new Date() } }
        ]
      },
      include: {
        budget: {
          include: {
            rates: {
              where: {
                OR: [
                  { validTo: null },
                  { validTo: { gte: new Date() } }
                ]
              },
              orderBy: { validFrom: 'desc' },
              take: 1
            }
          }
        },
        createdByStaff: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      }
    });

    res.json({
      success: true,
      data: currentAssignment
    });
  } catch (error) {
    console.error('Error fetching contract budget assignment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch contract budget assignment'
    });
  }
};

// Get contract budget assignment history
const getContractBudgetHistory = async (req, res) => {
  try {
    const { contractId } = req.params;

    const history = await prisma.operatorContractBudgetAssignment.findMany({
      where: {
        contractId: parseInt(contractId)
      },
      include: {
        budget: {
          include: {
            rates: {
              orderBy: { validFrom: 'desc' }
            }
          }
        },
        createdByStaff: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      },
      orderBy: { validFrom: 'desc' }
    });

    res.json({
      success: true,
      data: history
    });
  } catch (error) {
    console.error('Error fetching contract budget history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch contract budget history'
    });
  }
};

// Update contract budget assignment
const updateContractBudgetAssignment = async (req, res) => {
  try {
    const { contractId } = req.params;
    const { budgetCode, validFrom, validTo } = req.body;
    const staffId = req.user?.id || 1; // Default to admin for now

    // Close current assignment if it exists
    const currentAssignment = await prisma.operatorContractBudgetAssignment.findFirst({
      where: {
        contractId: parseInt(contractId),
        OR: [
          { validTo: null },
          { validTo: { gte: new Date() } }
        ]
      }
    });

    if (currentAssignment) {
      await prisma.operatorContractBudgetAssignment.update({
        where: { ocbaId: currentAssignment.ocbaId },
        data: {
          validTo: new Date(validFrom),
          updatedBy: staffId,
          updatedAt: new Date()
        }
      });
    }

    // Create new assignment
    const newAssignment = await prisma.operatorContractBudgetAssignment.create({
      data: {
        contractId: parseInt(contractId),
        budgetCode: budgetCode,
        validFrom: new Date(validFrom),
        validTo: validTo ? new Date(validTo) : null,
        createdBy: staffId
      },
      include: {
        budget: {
          include: {
            rates: {
              where: {
                OR: [
                  { validTo: null },
                  { validTo: { gte: new Date() } }
                ]
              },
              orderBy: { validFrom: 'desc' },
              take: 1
            }
          }
        },
        createdByStaff: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      }
    });

    res.json({
      success: true,
      data: newAssignment
    });
  } catch (error) {
    console.error('Error updating contract budget assignment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update contract budget assignment'
    });
  }
};

module.exports = {
  getBudgetCodes,
  getBudgetCodeHistory,
  updateBudgetCodeRate,
  getContractBudgetAssignment,
  getContractBudgetHistory,
  updateContractBudgetAssignment
};
