import { useEffect, useState } from 'react';
import { FaPlus } from 'react-icons/fa';
import { useOutletContext } from 'react-router-dom';
import { getCookie } from '../../../utils/helperFunctions';
import AuditLogPanel from './AuditLogPanel';
import IncidentAnalytics from './IncidentAnalytics';
import IncidentCard from './IncidentCard';
import ReportIncidentModal from './ReportIncidentModal';

export default function ContractIncidentsView() {
  const { selectedContract, selectedOperator } = useOutletContext();
  const [incidents, setIncidents] = useState([]);
  const [analytics, setAnalytics] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [showReportModal, setShowReportModal] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  // Fetch incidents when contract is selected
  useEffect(() => {
    if (selectedContract) {
      fetchIncidents(1, true);
      fetchAnalytics();
    } else {
      setIncidents([]);
      setAnalytics(null);
    }
  }, [selectedContract]);

  const fetchIncidents = async (pageNum = 1, reset = false) => {
    if (!selectedContract) return;

    setIsLoading(true);
    setError('');

    try {
      const token = getCookie('TMS_clientToken');
      const response = await fetch(
        `${import.meta.env.VITE_BACKEND_URL}/contracts/${selectedContract.id}/incidents?page=${pageNum}&limit=10`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      const data = await response.json();

      if (response.ok && data.success) {
        if (reset) {
          setIncidents(data.data);
        } else {
          setIncidents(prev => [...prev, ...data.data]);
        }
        setHasMore(data.pagination.page < data.pagination.totalPages);
        setPage(pageNum);
      } else {
        setError(data.message || 'Failed to fetch incidents');
      }
    } catch (error) {
      console.error('Error fetching incidents:', error);
      setError('Network error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchAnalytics = async () => {
    if (!selectedContract) return;

    try {
      const token = getCookie('TMS_clientToken');
      const response = await fetch(
        `${import.meta.env.VITE_BACKEND_URL}/contracts/${selectedContract.id}/incidents/analytics`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      const data = await response.json();

      if (response.ok && data.success) {
        setAnalytics(data.data);
      }
    } catch (error) {
      console.error('Error fetching analytics:', error);
    }
  };

  const handleReportSuccess = (newIncident) => {
    setIncidents(prev => [newIncident, ...prev]);
    setShowReportModal(false);
    fetchAnalytics(); // Refresh analytics
  };

  const loadMoreIncidents = () => {
    if (!isLoading && hasMore) {
      fetchIncidents(page + 1, false);
    }
  };

  const formatDateTime = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      if (diffInHours < 1) {
        return 'Just now';
      }
      return `${Math.floor(diffInHours)} hour${Math.floor(diffInHours) !== 1 ? 's' : ''} ago`;
    } else if (diffInHours < 48) {
      return `Yesterday – ${date.toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit' })}`;
    } else {
      return date.toLocaleDateString('en-GB') + ' – ' + date.toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit' });
    }
  };

  if (!selectedContract) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="text-gray-400 text-6xl mb-4">📋</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Contract Selected</h3>
          <p className="text-gray-500">
            Search for a transport operator and select a contract to view incidents
          </p>
        </div>
      </div>
    );
  }

  // If showing report modal, render only the modal
  if (showReportModal) {
    return (
      <ReportIncidentModal
        contractId={selectedContract?.id}
        onClose={() => setShowReportModal(false)}
        onSuccess={handleReportSuccess}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Banner */}
      {selectedContract && selectedOperator && (
        <div className="bg-white rounded-md shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">
                {selectedOperator.name}
              </h2>
              <div className="flex items-center gap-4 mt-1">
                <span className="text-sm text-gray-600">
                  Contract Code: <span className="font-medium">{selectedContract.code}</span>
                </span>
                <span className="text-sm text-gray-600">
                  Status: <span className={`font-medium ${selectedContract.isActive ? 'text-green-600' : 'text-red-600'}`}>
                    {selectedContract.isActive ? 'Active' : 'Inactive'}
                  </span>
                </span>
              </div>
            </div>
            <button
              onClick={() => setShowReportModal(true)}
              className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm"
            >
              <FaPlus className="mr-2" />
              Report New Incident
            </button>
          </div>
        </div>
      )}

      {/* Recent Activity Panel */}
      <AuditLogPanel contractId={selectedContract?.id} section="incidents" />

      {/* Recent Incidents */}
      <div className="bg-white rounded-md shadow-sm overflow-hidden">
        <div className="px-4 py-3 bg-white border-b border-black/[0.1] flex justify-between items-center">
          <h3 className="font-medium text-gray-700">Recent Incidents</h3>
        </div>
        <div className="p-4">
          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {error}
            </div>
          )}

          {incidents.length === 0 && !isLoading ? (
            <div className="text-center py-8">
              <div className="text-gray-400 text-4xl mb-4">📝</div>
              <p className="text-gray-500">No incidents reported for this contract</p>
            </div>
          ) : (
            <div className="space-y-4">
              {incidents.map((incident) => (
                <IncidentCard
                  key={incident.id}
                  incident={incident}
                  formatDateTime={formatDateTime}
                />
              ))}

              {hasMore && (
                <div className="text-center pt-4">
                  <button
                    onClick={loadMoreIncidents}
                    disabled={isLoading}
                    className="px-4 py-2 text-purple-600 hover:text-purple-800 disabled:opacity-50"
                  >
                    {isLoading ? 'Loading...' : 'Show More Incidents'}
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Incident Analytics */}
      <div className="bg-white rounded-md shadow-sm overflow-hidden">
        <div className="px-4 py-3 bg-white border-b border-black/[0.1]">
          <h3 className="font-medium text-gray-700">Incident Analytics</h3>
        </div>
        <div className="p-4">
          <IncidentAnalytics analytics={analytics} />
        </div>
      </div>


    </div>
  );
}
