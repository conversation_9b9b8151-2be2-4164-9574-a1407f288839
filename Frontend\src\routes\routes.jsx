import App from "../App";
import CodeView from "../components/clientViews/codesView/CodeView";
import AddNewContact from "../components/clientViews/contactsView/AddNewContact";
import ContactView from "../components/clientViews/contactsView/ContactView";
import EditContactInfo from "../components/clientViews/contactsView/EditContactInfo";
import AddCommunicationForm from "../components/clientViews/generalView/AddCommunicationForm";
import AddContactNoteForm from "../components/clientViews/generalView/AddContactNoteForm";
import AddNewStudentForm from "../components/clientViews/generalView/AddNewStudentForm";
import EditBasicInformation from "../components/clientViews/generalView/EditBasicInformation";
import GeneralView from "../components/clientViews/generalView/GeneralView";
import MedicalView from "../components/clientViews/medicalView/MedicalView";
import TravelView from "../components/clientViews/travelView/TravelView";
import ContractClientsView from "../components/contractViews/clientsView/ContractClientsView";
import ContractCostsView from "../components/contractViews/costsView/ContractCostsView";
import ContractFinancesView from "../components/contractViews/financesView/ContractFinancesView";
import AddNewContract from "../components/contractViews/generalView/AddNewContract";
import AddNewOperator from "../components/contractViews/generalView/AddNewOperator";
import EditContractDetails from "../components/contractViews/generalView/EditContractDetails";
import SimpleContractGeneralView from "../components/contractViews/generalView/SimpleContractGeneralView";
import ContractIncidentsView from "../components/contractViews/incidentsView/ContractIncidentsView";
import ReportNewIncident from "../components/contractViews/incidentsView/ReportNewIncident";

import Client from "../pages/Client";
import Contracts from "../pages/Contracts";
import Login from "../pages/Login";
import ResetPassword from "../pages/ResetPassword";
import AuthRedirect from "../utils/AuthRedirect";

const routes = [
  {
    path: "/login",
    element: <Login />
  },
  {
    path: "/reset-password",
    element: <ResetPassword />
  },

  {
    path: "/",
    element: <AuthRedirect />
  },

  {
    path: "/",
    element: <App />,
    children: [
      {
        path: "/client",
        element: <Client />,
        children: [
          {
            index: true,
            element: <GeneralView />
          },
          {
            path: "contacts",
            element: <ContactView />
          },
          {
            path: "contacts/add-new-contact",
            element: <AddNewContact />
          },
          {
            path: "contacts/edit-basic-info/:contactId",
            element: <EditContactInfo />
          },
          {
            path: "add-new-student",
            element: <AddNewStudentForm />
          },
          {
            path: "add-new-note",
            element: <AddContactNoteForm />
          },
          {
            path: "add-new-comm",
            element: <AddCommunicationForm />
          },
          {
            path: "edit-basic-info",
            element: <EditBasicInformation />
          },
          {
            path: "codes",
            element: <CodeView />
          },
          {
            path: "medical",
            element: <MedicalView />
          },
          {
            path: "travel",
            element: <TravelView />
          }
        ]
      },
      {
        path: "contracts",
        element: <Contracts />,
        children: [
          {
            index: true,
            element: <SimpleContractGeneralView />
          },
          {
            path: "incidents",
            element: <ContractIncidentsView />
          },
          {
            path: "add-new-contract",
            element: <AddNewContract />
          },
          {
            path: "add-new-operator",
            element: <AddNewOperator />
          },
          {
            path: "edit-contract-details",
            element: <EditContractDetails />
          },
          {
            path: "incidents/report-new-incident",
            element: <ReportNewIncident />
          },
          {
            path: "finances",
            element: <ContractFinancesView />
          },
          {
            path: "costs",
            element: <ContractCostsView />
          },
          {
            path: "clients",
            element: <ContractClientsView />
          }
        ]
      },
      {
        path: "establishments",
        element: <div className="p-6">Establishments Page Content</div>
      },
      {
        path: "finances",
        element: <div className="p-6">Finances Page Content</div>
      },
      {
        path: "analysis",
        element: <div className="p-6">Analysis Page Content</div>
      }
    ]
  }
];

export default routes;
