import { useEffect, useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import { getCookie } from '../../../utils/helperFunctions';
import AuditLogPanel from '../incidentsView/AuditLogPanel';
import CurrentActiveRate from './CurrentActiveRate';
import DailyRateBreakdown from './DailyRateBreakdown';
import RateHistory from './RateHistory';

export default function ContractCostsView() {
  const { selectedContract, selectedOperator } = useOutletContext();
  const [contractAssignment, setContractAssignment] = useState(null);
  const [contractHistory, setContractHistory] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // Fetch contract assignment when contract changes
  useEffect(() => {
    if (selectedContract?.id) {
      fetchContractAssignment();
      fetchContractHistory();
    }
  }, [selectedContract?.id]);

  const fetchContractAssignment = async () => {
    setIsLoading(true);
    try {
      const token = getCookie('TMS_clientToken');
      const response = await fetch(
        `${import.meta.env.VITE_BACKEND_URL}/finances/contracts/${selectedContract.id}/budget-assignment`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      const data = await response.json();
      if (response.ok && data.success) {
        setContractAssignment(data.data);
      } else {
        setError(data.message || 'Failed to fetch contract assignment');
      }
    } catch (error) {
      console.error('Error fetching contract assignment:', error);
      setError('Network error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchContractHistory = async () => {
    try {
      const token = getCookie('TMS_clientToken');
      const response = await fetch(
        `${import.meta.env.VITE_BACKEND_URL}/finances/contracts/${selectedContract.id}/budget-history`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      const data = await response.json();
      if (response.ok && data.success) {
        setContractHistory(data.data);
      }
    } catch (error) {
      console.error('Error fetching contract history:', error);
    }
  };

  if (!selectedContract || !selectedOperator) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-gray-400 text-4xl mb-4">💰</div>
          <p className="text-gray-500">Please select a contract to view costs</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Loading cost information...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Audit Log Panel */}
      <AuditLogPanel contractId={selectedContract?.id} />

      {/* Error Display */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {!contractAssignment ? (
        <div className="bg-white rounded-md shadow-sm p-8">
          <div className="text-center">
            <div className="text-gray-400 text-4xl mb-4">💰</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Budget Assignment</h3>
            <p className="text-gray-500 mb-4">
              This contract doesn't have a budget code assigned. Please assign a budget code in the Finances section first.
            </p>
            <button
              onClick={() => window.location.href = '/contracts/finances'}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
            >
              Go to Finances
            </button>
          </div>
        </div>
      ) : (
        <>
          {/* Current Active Rate */}
          <CurrentActiveRate assignment={contractAssignment} />

          {/* Rate History */}
          <RateHistory history={contractHistory} />

          {/* Daily Rate Breakdown */}
          <DailyRateBreakdown assignment={contractAssignment} />
        </>
      )}
    </div>
  );
}
