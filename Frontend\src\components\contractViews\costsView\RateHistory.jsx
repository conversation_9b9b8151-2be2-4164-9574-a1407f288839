export default function RateHistory({ history }) {
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-GB');
  };

  const getWeeklyRate = (dailyRate) => {
    return dailyRate * 5; // 5 school days in a week
  };

  const isActive = (assignment) => {
    const today = new Date();
    const validFrom = new Date(assignment.validFrom);
    const validTo = assignment.validTo ? new Date(assignment.validTo) : null;
    
    return validFrom <= today && (!validTo || validTo >= today);
  };

  const getStatusBadge = (assignment) => {
    const active = isActive(assignment);
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
        active 
          ? 'bg-green-100 text-green-800' 
          : 'bg-red-100 text-red-800'
      }`}>
        {active ? 'Active' : 'Expired'}
      </span>
    );
  };

  const getCurrentRate = (assignment) => {
    // Get the rate that was valid during this assignment period
    if (!assignment.budget?.rates || assignment.budget.rates.length === 0) return null;

    // Find the rate that was active during this assignment period
    const assignmentStart = new Date(assignment.validFrom);
    const assignmentEnd = assignment.validTo ? new Date(assignment.validTo) : new Date();

    const validRate = assignment.budget.rates.find(rate => {
      const rateStart = new Date(rate.validFrom);
      const rateEnd = rate.validTo ? new Date(rate.validTo) : new Date();

      // Check if rate period overlaps with assignment period
      return rateStart <= assignmentEnd && rateEnd >= assignmentStart;
    });

    // If no overlapping rate found, return the most recent one
    return validRate || assignment.budget.rates[0];
  };

  return (
    <div className="bg-white rounded-md shadow-sm overflow-hidden">
      <div className="px-4 py-3 bg-white border-b border-black/[0.1]">
        <h3 className="font-medium text-gray-700">Rate History</h3>
      </div>

      <div className="overflow-x-auto">
        {history.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-400 text-3xl mb-2">📋</div>
            <p className="text-gray-500">No rate history available</p>
          </div>
        ) : (
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Valid From
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Valid To
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Budget Code
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Reason
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Weekly Rate
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {history.map((assignment) => {
                const currentRate = getCurrentRate(assignment);
                const weeklyRate = currentRate ? getWeeklyRate(currentRate.rate) : 0;
                
                return (
                  <tr key={assignment.ocbaId} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatDate(assignment.validFrom)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {assignment.validTo ? formatDate(assignment.validTo) : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {assignment.budgetCode}
                        </div>
                        <div className="text-sm text-gray-500">
                          {assignment.budget?.name}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">
                      {currentRate?.reason || 'Standard rate'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {formatCurrency(weeklyRate)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(assignment)}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        )}
      </div>
    </div>
  );
}
