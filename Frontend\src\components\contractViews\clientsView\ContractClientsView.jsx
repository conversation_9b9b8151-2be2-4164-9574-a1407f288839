import { useState, useEffect } from 'react';
import { useOutletContext } from 'react-router-dom';
import { getCookie } from '../../../utils/helperFunctions';
import AuditLogPanel from '../incidentsView/AuditLogPanel';
import CapacityIndicator from './CapacityIndicator';
import AddNewClient from './AddNewClient';
import ClientList from './ClientList';

export default function ContractClientsView() {
  const { selectedContract, selectedOperator } = useOutletContext();
  const [clients, setClients] = useState([]);
  const [capacity, setCapacity] = useState({ filled: 0, max: 0, available: 0 });
  const [pagination, setPagination] = useState({ page: 1, limit: 10, total: 0, totalPages: 0 });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // Fetch clients when contract changes
  useEffect(() => {
    if (selectedContract?.id) {
      fetchClients();
    }
  }, [selectedContract?.id, pagination.page]);

  const fetchClients = async () => {
    setIsLoading(true);
    try {
      const token = getCookie('TMS_clientToken');
      const response = await fetch(
        `${import.meta.env.VITE_BACKEND_URL}/contracts/${selectedContract.id}/clients?page=${pagination.page}&limit=${pagination.limit}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      const data = await response.json();
      if (response.ok && data.success) {
        setClients(data.data.students);
        setCapacity(data.data.capacity);
        setPagination(prev => ({
          ...prev,
          total: data.data.pagination.total,
          totalPages: data.data.pagination.totalPages
        }));
      } else {
        setError(data.message || 'Failed to fetch clients');
      }
    } catch (error) {
      console.error('Error fetching clients:', error);
      setError('Network error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClientAdded = () => {
    // Refresh the client list and capacity
    fetchClients();
  };

  const handleClientRemoved = () => {
    // Refresh the client list and capacity
    fetchClients();
  };

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  if (!selectedContract || !selectedOperator) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-gray-400 text-4xl mb-4">👥</div>
          <p className="text-gray-500">Please select a contract to view clients</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Audit Log Panel */}
      <AuditLogPanel contractId={selectedContract?.id} />

      {/* Error Display */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {/* Capacity Indicator */}
      <CapacityIndicator capacity={capacity} />

      {/* Add New Client */}
      <AddNewClient 
        contractId={selectedContract.id}
        onClientAdded={handleClientAdded}
      />

      {/* Client List */}
      <ClientList
        clients={clients}
        contractId={selectedContract.id}
        pagination={pagination}
        isLoading={isLoading}
        onClientRemoved={handleClientRemoved}
        onPageChange={handlePageChange}
      />
    </div>
  );
}
