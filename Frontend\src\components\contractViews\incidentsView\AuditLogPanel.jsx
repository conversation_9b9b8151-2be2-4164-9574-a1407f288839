import { useEffect, useState } from 'react';
import { FaHistory } from 'react-icons/fa';
import { getCookie } from '../../../utils/helperFunctions';

export default function AuditLogPanel({ contractId }) {
  const [auditLogs, setAuditLogs] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (contractId) {
      fetchAuditLogs();
    }
  }, [contractId]);

  const fetchAuditLogs = async () => {
    setIsLoading(true);
    setError('');
    
    try {
      const token = getCookie('TMS_clientToken');
      const response = await fetch(
        `${import.meta.env.VITE_BACKEND_URL}/contracts/${contractId}/audit-logs`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      const data = await response.json();
      
      if (response.ok && data.success) {
        setAuditLogs(data.data);
      } else {
        setError(data.message || 'Failed to fetch audit logs');
      }
    } catch (error) {
      console.error('Error fetching audit logs:', error);
      setError('Network error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDateTime = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      if (diffInHours < 1) {
        return 'Just now';
      }
      return `${Math.floor(diffInHours)} hour${Math.floor(diffInHours) !== 1 ? 's' : ''} ago`;
    } else if (diffInHours < 48) {
      return `Yesterday – ${date.toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit' })}`;
    } else {
      return date.toLocaleDateString('en-GB') + ' – ' + date.toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit' });
    }
  };

  const getOperationColor = (operation) => {
    switch (operation.toLowerCase()) {
      case 'create':
        return 'text-green-600 bg-green-100';
      case 'update':
        return 'text-blue-600 bg-blue-100';
      case 'delete':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getOperationText = (operation, entity) => {
    const entityName = entity.toLowerCase().replace('_', ' ');
    switch (operation.toLowerCase()) {
      case 'create':
        return `Created ${entityName}`;
      case 'update':
        return `Updated ${entityName}`;
      case 'delete':
        return `Deleted ${entityName}`;
      default:
        return `Modified ${entityName}`;
    }
  };

  if (!contractId) {
    return null;
  }

  return (
    <div className="bg-white rounded-md shadow-sm overflow-hidden">
      <div className="px-4 py-3 bg-white border-b border-black/[0.1] flex items-center">
        <FaHistory className="mr-2 text-gray-500" />
        <h3 className="font-medium text-gray-700">Recent Activity</h3>
      </div>
      
      <div className="p-4">
        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded text-sm">
            {error}
          </div>
        )}

        {isLoading ? (
          <div className="text-center py-4 text-gray-500 text-sm">
            Loading activity...
          </div>
        ) : auditLogs.length === 0 ? (
          <div className="text-center py-6">
            <div className="text-gray-400 text-3xl mb-2">📋</div>
            <p className="text-gray-500 text-sm">No recent activity</p>
          </div>
        ) : (
          <div className="space-y-2">
            {auditLogs.slice(0, 5).map((log) => (
              <div key={log.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <div>
                    <p className="text-sm text-gray-900">
                      {getOperationText(log.operation, log.entity)}
                    </p>
                    <p className="text-xs text-gray-500">
                      by {log.staff?.firstName} {log.staff?.lastName}
                    </p>
                  </div>
                </div>

                <div className="text-xs text-gray-500">
                  {formatDateTime(log.changedAt)}
                </div>
              </div>
            ))}

            {auditLogs.length > 5 && (
              <div className="text-center pt-3">
                <button className="text-xs text-purple-600 hover:text-purple-800">
                  View All
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
