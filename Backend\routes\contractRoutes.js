// /routes/contractRoutes.js
const express = require('express');
const {
  getOperators,
  getOperatorContracts,
  getContractDetails,
  updateContract,
  createOperator,
  createContract
} = require('../controllers/contractController');
const { getBudgetCodes } = require('../controllers/financeController');
const { 
  getContractIncidents, 
  getIncidentAnalytics, 
  createIncident, 
  getIncidentTypes 
} = require('../controllers/incidentController');
const { verifyToken } = require('../middlewares/authMiddleware');
const { PrismaClient } = require('../generated/prisma/client');
const prisma = new PrismaClient();

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

/**
 * @route   GET /api/contracts/budget-codes
 * @desc    Get all budget codes for contract forms
 * @access  Private
 */
router.get('/budget-codes', getBudgetCodes);

/**
 * @route   GET /api/contracts/operators
 * @desc    Get transport operators with search functionality
 * @access  Private
 */
router.get('/operators', getOperators);

/**
 * @route   POST /api/contracts/operators
 * @desc    Create new transport operator
 * @access  Private
 */
router.post('/operators', createOperator);

/**
 * @route   GET /api/contracts/incident-types
 * @desc    Get all incident types
 * @access  Private
 */
router.get('/incident-types', getIncidentTypes);

/**
 * @route   GET /api/contracts/operators/:operatorId/contracts
 * @desc    Get contracts for a specific operator
 * @access  Private
 */
router.get('/operators/:operatorId/contracts', getOperatorContracts);

/**
 * @route   GET /api/contracts/:contractId
 * @desc    Get contract details
 * @access  Private
 */
router.get('/:contractId', getContractDetails);

/**
 * @route   POST /api/contracts
 * @desc    Create new contract
 * @access  Private
 */
router.post('/', createContract);

/**
 * @route   PUT /api/contracts/:contractId
 * @desc    Update contract details
 * @access  Private
 */
router.put('/:contractId', updateContract);

/**
 * @route   GET /api/contracts/:contractId/incidents
 * @desc    Get incidents for a contract
 * @access  Private
 */
router.get('/:contractId/incidents', getContractIncidents);

/**
 * @route   GET /api/contracts/:contractId/incidents/analytics
 * @desc    Get incident analytics for a contract
 * @access  Private
 */
router.get('/:contractId/incidents/analytics', getIncidentAnalytics);

/**
 * @route   POST /api/contracts/:contractId/incidents
 * @desc    Create new incident for a contract
 * @access  Private
 */
router.post('/:contractId/incidents', createIncident);

/**
 * @route   GET /api/contracts/:contractId/audit-logs
 * @desc    Get audit logs for a contract
 * @access  Private
 */
router.get('/:contractId/audit-logs', async (req, res) => {
  try {
    const { contractId } = req.params;
    const { PrismaClient } = require('../generated/prisma/client');
    const prisma = new PrismaClient();

    const auditLogs = await prisma.auditLog.findMany({
      where: {
        OR: [
          {
            entity: 'operator_contract',
            entityId: parseInt(contractId)
          },
          {
            entity: 'contract_incident',
            entityId: parseInt(contractId)
          }
        ]
      },
      include: {
        staff: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      },
      orderBy: {
        changedAt: 'desc'
      },
      take: 10
    });

    res.json({
      success: true,
      data: auditLogs
    });
  } catch (error) {
    console.error('Error fetching audit logs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch audit logs'
    });
  }
});

/**
 * @route   GET /api/contracts/:contractId/clients
 * @desc    Get all clients assigned to a contract with capacity info
 * @access  Private
 */
router.get('/:contractId/clients', async (req, res) => {
  try {
    const { contractId } = req.params;
    const { page = 1, limit = 10 } = req.query;

    const skip = (page - 1) * limit;

    // Get contract details with max students
    const contract = await prisma.operatorContract.findUnique({
      where: { id: parseInt(contractId) }
    });

    if (!contract) {
      return res.status(404).json({
        success: false,
        message: 'Contract not found'
      });
    }

    // Get all assignments for this contract
    const assignments = await prisma.studentTravelAssignment.findMany({
      where: { operatorContractId: parseInt(contractId) },
      include: {
        student: {
          include: {
            establishment: true,
            clientType: true,
            medicalRecords: true,
            codeAssignments: {
              where: {
                validTo: null // Current assignment
              },
              include: {
                eligibilityCode: true,
                budgetCode: true
              }
            }
          }
        }
      }
    });

    // Deduplicate students (same student might have AM and PM assignments)
    const uniqueStudents = Object.values(
      assignments.reduce((acc, assignment) => {
        const studentId = assignment.student.id;
        if (!acc[studentId]) {
          acc[studentId] = {
            ...assignment.student,
            runTypes: [assignment.runType],
            maxTravelTimeMins: assignment.maxTravelTimeMins
          };
        } else {
          acc[studentId].runTypes.push(assignment.runType);
        }
        return acc;
      }, {})
    );

    // Apply pagination
    const totalStudents = uniqueStudents.length;
    const paginatedStudents = uniqueStudents.slice(skip, skip + parseInt(limit));

    // Calculate capacity
    const filledSeats = totalStudents;
    const maxSeats = contract.maxStudents || 0;
    const availableSeats = Math.max(0, maxSeats - filledSeats);

    res.json({
      success: true,
      data: {
        students: paginatedStudents,
        capacity: {
          filled: filledSeats,
          max: maxSeats,
          available: availableSeats
        },
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: totalStudents,
          totalPages: Math.ceil(totalStudents / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching contract clients:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch contract clients'
    });
  }
});

/**
 * @route   GET /api/contracts/:contractId/search-students
 * @desc    Search students for assignment to contract
 * @access  Private
 */
router.get('/:contractId/search-students', async (req, res) => {
  try {
    const { contractId } = req.params;
    const { query } = req.query;

    if (!query || query.trim().length < 2) {
      return res.json({
        success: true,
        data: []
      });
    }

    const searchTerm = query.trim();
    const isNumeric = !isNaN(searchTerm);

    // Build search conditions
    const searchConditions = {
      OR: [
        { surname: { contains: searchTerm, mode: 'insensitive' } },
        { knownAs: { contains: searchTerm, mode: 'insensitive' } }
      ]
    };

    // If numeric, also search by ID
    if (isNumeric) {
      searchConditions.OR.push({ id: parseInt(searchTerm) });
    }

    const students = await prisma.student.findMany({
      where: searchConditions,
      include: {
        establishment: true,
        clientType: true,
        medicalRecords: true,
        codeAssignments: {
          where: {
            validTo: null // Current assignment
          },
          include: {
            budgetCode: true
          }
        },
        travelAssignments: {
          where: {
            operatorContractId: parseInt(contractId)
          }
        }
      },
      take: 20 // Limit search results
    });

    // Filter out students already assigned to this contract
    const availableStudents = students.filter(student =>
      student.travelAssignments.length === 0
    );

    res.json({
      success: true,
      data: availableStudents
    });
  } catch (error) {
    console.error('Error searching students:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to search students'
    });
  }
});

/**
 * @route   POST /api/contracts/:contractId/clients
 * @desc    Assign a student to a contract
 * @access  Private
 */
router.post('/:contractId/clients', async (req, res) => {
  try {
    const { contractId } = req.params;
    const { studentId, runType, maxTravelTimeMins } = req.body;
    const userId = req.user.id;

    // Validate required fields
    if (!studentId || !runType) {
      return res.status(400).json({
        success: false,
        message: 'Student ID and run type are required'
      });
    }

    // Validate run type
    if (!['AM', 'PM', 'Both'].includes(runType)) {
      return res.status(400).json({
        success: false,
        message: 'Run type must be AM, PM, or Both'
      });
    }

    // Get contract details
    const contract = await prisma.operatorContract.findUnique({
      where: { id: parseInt(contractId) },
      include: {
        budgetAssignments: {
          where: {
            validTo: null // Current assignment
          }
        }
      }
    });

    if (!contract) {
      return res.status(404).json({
        success: false,
        message: 'Contract not found'
      });
    }

    // Get student details
    const student = await prisma.student.findUnique({
      where: { id: parseInt(studentId) },
      include: {
        codeAssignments: {
          where: {
            validTo: null // Current assignment
          }
        },
        travelAssignments: {
          where: {
            operatorContractId: parseInt(contractId)
          }
        }
      }
    });

    if (!student) {
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      });
    }

    // Check if student is already assigned to this contract
    if (student.travelAssignments.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Student is already assigned to this contract'
      });
    }

    // Check budget code match
    const contractBudgetCode = contract.budgetAssignments[0]?.budgetCode;
    const studentBudgetCode = student.codeAssignments[0]?.budgetCode;

    if (!contractBudgetCode || !studentBudgetCode) {
      return res.status(400).json({
        success: false,
        message: 'Both contract and student must have valid budget codes'
      });
    }

    if (contractBudgetCode !== studentBudgetCode) {
      return res.status(400).json({
        success: false,
        message: 'Student budget code must match contract budget code'
      });
    }

    // Check capacity
    const currentAssignments = await prisma.studentTravelAssignment.findMany({
      where: { operatorContractId: parseInt(contractId) }
    });

    const uniqueStudentIds = new Set(currentAssignments.map(a => a.studentId));
    const currentCapacity = uniqueStudentIds.size;

    if (contract.maxStudents && currentCapacity >= contract.maxStudents) {
      return res.status(400).json({
        success: false,
        message: 'Contract is at maximum capacity'
      });
    }

    // Get current term (simplified - you might want to make this more sophisticated)
    const currentTerm = await prisma.establishmentTermPeriod.findFirst({
      where: {
        startDate: { lte: new Date() },
        endDate: { gte: new Date() }
      }
    });

    if (!currentTerm) {
      return res.status(400).json({
        success: false,
        message: 'No active term period found'
      });
    }

    // Create assignments based on run type
    const assignmentsToCreate = [];

    if (runType === 'Both') {
      assignmentsToCreate.push(
        {
          studentId: parseInt(studentId),
          operatorContractId: parseInt(contractId),
          termId: currentTerm.id,
          runType: 'AM',
          maxTravelTimeMins: maxTravelTimeMins ? parseInt(maxTravelTimeMins) : null
        },
        {
          studentId: parseInt(studentId),
          operatorContractId: parseInt(contractId),
          termId: currentTerm.id,
          runType: 'PM',
          maxTravelTimeMins: maxTravelTimeMins ? parseInt(maxTravelTimeMins) : null
        }
      );
    } else {
      assignmentsToCreate.push({
        studentId: parseInt(studentId),
        operatorContractId: parseInt(contractId),
        termId: currentTerm.id,
        runType: runType,
        maxTravelTimeMins: maxTravelTimeMins ? parseInt(maxTravelTimeMins) : null
      });
    }

    // Create assignments in transaction
    const result = await prisma.$transaction(async (tx) => {
      const createdAssignments = [];

      for (const assignmentData of assignmentsToCreate) {
        const assignment = await tx.studentTravelAssignment.create({
          data: assignmentData,
          include: {
            student: {
              include: {
                establishment: true,
                clientType: true
              }
            }
          }
        });
        createdAssignments.push(assignment);
      }

      // Create audit log
      await tx.auditLog.create({
        data: {
          entity: 'student_travel_assignment',
          entityId: createdAssignments[0].id,
          operation: 'CREATE',
          changedBy: userId,
          changes: JSON.stringify({
            action: 'assign_student_to_contract',
            studentId: parseInt(studentId),
            contractId: parseInt(contractId),
            runType: runType
          })
        }
      });

      return createdAssignments;
    });

    res.json({
      success: true,
      data: result,
      message: 'Student assigned to contract successfully'
    });
  } catch (error) {
    console.error('Error assigning student to contract:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to assign student to contract'
    });
  }
});

/**
 * @route   DELETE /api/contracts/:contractId/clients/:studentId
 * @desc    Remove a student from a contract
 * @access  Private
 */
router.delete('/:contractId/clients/:studentId', async (req, res) => {
  try {
    const { contractId, studentId } = req.params;
    const userId = req.user.id;

    // Find all assignments for this student and contract
    const assignments = await prisma.studentTravelAssignment.findMany({
      where: {
        studentId: parseInt(studentId),
        operatorContractId: parseInt(contractId)
      },
      include: {
        student: true
      }
    });

    if (assignments.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Student assignment not found'
      });
    }

    // Delete all assignments (AM and PM) in transaction
    const result = await prisma.$transaction(async (tx) => {
      // Delete all assignments
      const deletedAssignments = await tx.studentTravelAssignment.deleteMany({
        where: {
          studentId: parseInt(studentId),
          operatorContractId: parseInt(contractId)
        }
      });

      // Create audit log
      await tx.auditLog.create({
        data: {
          entity: 'student_travel_assignment',
          entityId: assignments[0].id,
          operation: 'DELETE',
          changedBy: userId,
          changes: JSON.stringify({
            action: 'remove_student_from_contract',
            studentId: parseInt(studentId),
            contractId: parseInt(contractId),
            deletedCount: deletedAssignments.count
          })
        }
      });

      return deletedAssignments;
    });

    res.json({
      success: true,
      data: result,
      message: 'Student removed from contract successfully'
    });
  } catch (error) {
    console.error('Error removing student from contract:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove student from contract'
    });
  }
});

module.exports = router;
