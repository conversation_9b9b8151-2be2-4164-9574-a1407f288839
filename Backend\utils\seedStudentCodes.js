const { PrismaClient } = require('../generated/prisma/client');
const prisma = new PrismaClient();

async function seedStudentCodes() {
  console.log('Creating student code assignments...');

  try {
    // Get all students
    const students = await prisma.student.findMany();
    console.log(`Found ${students.length} students`);

    // Create code assignments for each student
    for (const student of students) {
      // Check if student already has a code assignment
      const existingAssignment = await prisma.studentCodeAssignment.findFirst({
        where: {
          studentId: student.id,
          validTo: null
        }
      });

      if (!existingAssignment) {
        // Assign budget code based on client type
        let budgetCode = 'ELA001'; // Default to Core Statutory Transport
        let eligibilityCode = 'E001'; // Statutory Distance
        let fundingCode = 'LA_HTS'; // LA Funded Home-to-School

        if (student.clientTypeId === 2) { // SEN Transport
          budgetCode = 'ELA002'; // Enhanced SEN Transport
          eligibilityCode = 'E003'; // SEN / EHCP
          fundingCode = 'SEN_HTS'; // EHCP/SEN Specialist Funding
        }

        await prisma.studentCodeAssignment.create({
          data: {
            studentId: student.id,
            eligibilityCode: eligibilityCode,
            budgetCode: budgetCode,
            fundingCode: fundingCode,
            validFrom: new Date('2024-09-01'),
            validTo: null,
            createdBy: 1 // Admin user
          }
        });

        console.log(`Created code assignment for ${student.firstName} ${student.surname} with budget code ${budgetCode}`);
      } else {
        console.log(`${student.firstName} ${student.surname} already has a code assignment`);
      }
    }

    // Also create some term periods if they don't exist
    const existingTerm = await prisma.establishmentTermPeriod.findFirst();
    if (!existingTerm) {
      console.log('Creating term periods...');
      
      const establishments = await prisma.establishment.findMany();
      for (const establishment of establishments) {
        await prisma.establishmentTermPeriod.create({
          data: {
            estabId: establishment.id,
            termName: 'Autumn Term 2024',
            startDate: new Date('2024-09-01'),
            endDate: new Date('2024-12-20'),
            createdBy: 1
          }
        });

        await prisma.establishmentTermPeriod.create({
          data: {
            estabId: establishment.id,
            termName: 'Spring Term 2025',
            startDate: new Date('2025-01-06'),
            endDate: new Date('2025-04-04'),
            createdBy: 1
          }
        });

        await prisma.establishmentTermPeriod.create({
          data: {
            estabId: establishment.id,
            termName: 'Summer Term 2025',
            startDate: new Date('2025-04-21'),
            endDate: new Date('2025-07-18'),
            createdBy: 1
          }
        });

        console.log(`Created term periods for ${establishment.name}`);
      }
    }

    console.log('✅ Student code assignments completed!');
  } catch (error) {
    console.error('Error creating student code assignments:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

seedStudentCodes()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });
