const { PrismaClient, ClientTypeCode, MedicalNeedCode, YearGroupValue } = require('../generated/prisma/client.js');

const prisma = new PrismaClient();

async function main() {
  // Seed ClientType data using upsert
  const clientTypes = [
    {
      id: 1,
      code: ClientTypeCode.HTS,
      name: "Home to School",
      description: "Statutory free transport for eligible pupils based on distance and age criteria."
    },
    {
      id: 2,
      code: ClientTypeCode.SEN,
      name: "SEN Transport",
      description: "Specialist transport for pupils with an Education, Health & Care Plan EHCP."
    },
    {
      id: 3,
      code: ClientTypeCode.LAC,
      name: "Looked-After Child",
      description: "Free transport for children in care, as arranged by the local authority."
    },
    {
      id: 4,
      code: ClientTypeCode.DIS,
      name: "Discretionary Transport",
      description: "Non-statutory travel provided at the LA's discretion (e.g. safety, hardship cases)."
    },
    {
      id: 5,
      code: ClientTypeCode.PRIV,
      name: "Private / Self-Funded",
      description: "Parents purchase transport privately; not funded by the LA."
    }
  ];

  for (const clientType of clientTypes) {
    await prisma.clientType.upsert({
      where: { code: clientType.code },
      update: {
        name: clientType.name,
        description: clientType.description
      },
      create: clientType
    });
  }

  // Seed MedicalNeed data using upsert
  const medicalNeeds = [
    {
      id: 1,
      code: MedicalNeedCode.ASTHMA,
      description: "Asthma"
    },
    {
      id: 2,
      code: MedicalNeedCode.ASD,
      description: "Autism Spectrum Disorder"
    },
    {
      id: 3,
      code: MedicalNeedCode.DIABETES,
      description: "Diabetes Type 1 or Type 2"
    },
    {
      id: 4,
      code: MedicalNeedCode.EPILEPSY,
      description: "Epilepsy"
    },
    {
      id: 5,
      code: MedicalNeedCode.ADHD,
      description: "Attention Deficit Hyperactivity Disorder"
    }
  ];

  for (const medicalNeed of medicalNeeds) {
    await prisma.medicalNeed.upsert({
      where: { id: medicalNeed.id },
      update: {
        description: medicalNeed.description
      },
      create: medicalNeed
    });
  }

  // Seed YearGroup data using upsert
  const yearGroups = [
    {
      id: 1,
      code: YearGroupValue.YR_7,
      name: "Yr 7"
    },
    {
      id: 2,
      code: YearGroupValue.YR_8,
      name: "Yr 8"
    },
    {
      id: 3,
      code: YearGroupValue.YR_9,
      name: "Yr 9"
    },
    {
      id: 4,
      code: YearGroupValue.YR_10,
      name: "Yr 10"
    },
    {
      id: 5,
      code: YearGroupValue.YR_11,
      name: "Yr 11"
    }
  ];

  for (const yearGroup of yearGroups) {
    await prisma.yearGroup.upsert({
      where: { code: yearGroup.code },
      update: {
        name: yearGroup.name
      },
      create: yearGroup
    });
  }

  // Seed EligibilityCode data using upsert
  const eligibilityCodes = [
    {
      code: "E001",
      name: "Statutory Distance",
      description: "Student lives beyond the legal walking distance (2 miles for under 8s, 3 miles for over 8s)"
    },
    {
      code: "E002",
      name: "Low-Income",
      description: "Family qualifies under low-income criteria (e.g. FSM or maximum Working Tax Credit)"
    },
    {
      code: "E003",
      name: "SEN / EHCP",
      description: "Student has an Education, Health & Care Plan requiring transport support"
    },
    {
      code: "E004",
      name: "Disability",
      description: "Student has a long-term disability affecting independent travel"
    },
    {
      code: "E005",
      name: "Unsafe Route",
      description: "The walking route is assessed as unsafe by the LA"
    },
    {
      code: "E006",
      name: "Temporary Placement",
      description: "Short-term education placement requiring transport (e.g. PRU or hospital)"
    },
    {
      code: "E007",
      name: "LAC / Foster Placement",
      description: "Child is in care or with a foster family needing transport"
    },
    {
      code: "E008",
      name: "Family Crisis / Hardship",
      description: "Exceptional social or welfare issues requiring discretionary travel"
    }
  ];

  for (const eligibilityCode of eligibilityCodes) {
    await prisma.eligibilityCode.upsert({
      where: { code: eligibilityCode.code },
      update: {
        name: eligibilityCode.name,
        description: eligibilityCode.description
      },
      create: eligibilityCode
    });
  }

  // Seed BudgetCode data using upsert
  const budgetCodes = [
    {
      code: "ELA001",
      name: "Core Statutory Transport",
      description: "Main budget for pupils eligible under statutory criteria"
    },
    {
      code: "ELA002",
      name: "Enhanced SEN Transport",
      description: "Additional cost for students with EHCP or medical needs"
    },
    {
      code: "ELA003",
      name: "Discretionary Transport",
      description: "Local Authority discretionary transport allocation"
    },
    {
      code: "ELA004",
      name: "LAC Funded Travel",
      description: "Budget line for Looked-After Children"
    },
    {
      code: "ELA005",
      name: "Rehousing / Temp Accommodation",
      description: "For pupils in temporary or emergency housing"
    },
    {
      code: "ELA006",
      name: "Joint Funded (e.g. LA + NHS)",
      description: "Transport funded jointly with other agencies (NHS, school, etc.)"
    },
    {
      code: "ELA007",
      name: "Pupil Referral Unit Support",
      description: "Specific budget for PRU transport cases"
    },
    {
      code: "ELA008",
      name: "Appeals Panel Award",
      description: "Budget for cases approved by school transport appeal panels"
    }
  ];

  for (const budgetCode of budgetCodes) {
    await prisma.budgetCode.upsert({
      where: { code: budgetCode.code },
      update: {
        name: budgetCode.name,
        description: budgetCode.description
      },
      create: budgetCode
    });
  }

  // Seed FundingCode data using upsert
  const fundingCodes = [
    {
      code: "LA_HTS",
      name: "LA Funded Home-to-School",
      description: "Standard local authority transport funding"
    },
    {
      code: "SEN_HTS",
      name: "EHCP/SEN Specialist Funding",
      description: "Additional SEN funding (may include higher-cost contracts)"
    },
    {
      code: "LAC_HTS",
      name: "Looked-After Children Transport",
      description: "Covered under social care or virtual school budget"
    },
    {
      code: "DIS_HTS",
      name: "Discretionary Travel Fund",
      description: "LA-funded under exceptional or discretionary rules"
    },
    {
      code: "PVT_SELF",
      name: "Private/Self-Funded",
      description: "Parent or carer pays for transport"
    },
    {
      code: "JOINT_FUND",
      name: "Joint Funded (e.g. LA + Other)",
      description: "Shared funding between LA and other entities (e.g. NHS, school)"
    },
    {
      code: "NHS_FUNDED",
      name: "NHS Reimbursement",
      description: "Fully covered by NHS in medical circumstances"
    },
    {
      code: "CRISIS_FND",
      name: "Emergency / Crisis Funded",
      description: "Temporary transport during family crises or safeguarding cases"
    }
  ];

  for (const fundingCode of fundingCodes) {
    await prisma.fundingCode.upsert({
      where: { code: fundingCode.code },
      update: {
        name: fundingCode.name,
        description: fundingCode.description
      },
      create: fundingCode
    });
  }

  // Create sample staff users
  const bcrypt = require('bcryptjs');
  const hashedPassword = await bcrypt.hash('admin123', 10);

  const sampleStaff = [
    {
      firstName: 'John',
      lastName: 'Admin',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'Admin',
      isActive: true
    },
    {
      firstName: 'Jane',
      lastName: 'Manager',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'Manager',
      isActive: true
    }
  ];

  for (const staff of sampleStaff) {
    await prisma.staff.upsert({
      where: { email: staff.email },
      update: staff,
      create: staff
    });
  }

  // Create sample establishments
  const sampleEstablishments = [
    {
      name: 'Hill Park Upper School',
      addressLine1: '123 School Lane',
      city: 'Brighton',
      postcode: 'BN1 2AB',
      schoolType: 'SEC',
      telephone: '01273 123456',
      email: '<EMAIL>'
    },
    {
      name: 'Meadow Primary School',
      addressLine1: '456 Education Road',
      city: 'Hove',
      postcode: 'BN3 4CD',
      schoolType: 'PRIM',
      telephone: '01273 654321',
      email: '<EMAIL>'
    }
  ];

  for (const establishment of sampleEstablishments) {
    // Check if establishment already exists
    const existingEstablishment = await prisma.establishment.findFirst({
      where: {
        name: establishment.name
      }
    });

    if (!existingEstablishment) {
      await prisma.establishment.create({
        data: establishment
      });
    }
  }

  // Create sample students
  const sampleStudents = [
    {
      title: 'Miss',
      firstName: 'Emma',
      surname: 'Johnson',
      knownAs: 'Em',
      gender: 'F',
      dateOfBirth: new Date('2010-05-15'),
      clientTypeId: 1, // HTS
      requiresTravel: true,
      inCare: false,
      yearGroupId: 1, // YR_7
      estabId: 1,
      isActive: true
    },
    {
      title: 'Mr',
      firstName: 'James',
      surname: 'Smith',
      knownAs: 'Jamie',
      gender: 'M',
      dateOfBirth: new Date('2009-08-22'),
      clientTypeId: 2, // SEN
      requiresTravel: true,
      inCare: false,
      medicalNeedId: 2, // ASD
      yearGroupId: 2, // YR_8
      estabId: 1,
      isActive: true
    },
    {
      title: 'Miss',
      firstName: 'Sophie',
      surname: 'Williams',
      knownAs: 'Soph',
      gender: 'F',
      dateOfBirth: new Date('2011-12-03'),
      clientTypeId: 1, // HTS
      requiresTravel: true,
      inCare: false,
      yearGroupId: 1, // YR_7
      estabId: 2,
      isActive: true
    }
  ];

  for (const student of sampleStudents) {
    // Check if student already exists
    const existingStudent = await prisma.student.findFirst({
      where: {
        firstName: student.firstName,
        surname: student.surname,
        dateOfBirth: student.dateOfBirth
      }
    });

    if (!existingStudent) {
      await prisma.student.create({
        data: student
      });
    }
  }

  // Get the first staff member for foreign key reference
  const firstStaff = await prisma.staff.findFirst();

  // Get the created students for foreign key reference
  const students = await prisma.student.findMany({
    where: {
      firstName: { in: ['Emma', 'James', 'Sophie'] }
    }
  });

  // Create sample contacts for students
  const sampleContacts = [
    {
      studentId: students.find(s => s.firstName === 'Emma')?.id,
      title: 'Mrs',
      firstname: 'Sarah',
      surname: 'Johnson',
      relationship: 'Mother',
      isPrimary: true,
      isPayer: true
    },
    {
      studentId: students.find(s => s.firstName === 'James')?.id,
      title: 'Mr',
      firstname: 'David',
      surname: 'Smith',
      relationship: 'Father',
      isPrimary: true,
      isPayer: true
    },
    {
      studentId: students.find(s => s.firstName === 'Sophie')?.id,
      title: 'Mrs',
      firstname: 'Lisa',
      surname: 'Williams',
      relationship: 'Mother',
      isPrimary: true,
      isPayer: true
    }
  ];

  for (const contact of sampleContacts) {
    const existingContact = await prisma.studentContact.findFirst({
      where: {
        studentId: contact.studentId,
        firstname: contact.firstname,
        surname: contact.surname
      }
    });

    if (!existingContact) {
      const createdContact = await prisma.studentContact.create({
        data: contact
      });

      // Add contact methods (phone and email)
      await prisma.studentContactMethod.createMany({
        data: [
          {
            contactId: createdContact.id,
            methodType: 'mobile',
            value: `07${Math.floor(Math.random() * 900000000) + 100000000}`
          },
          {
            contactId: createdContact.id,
            methodType: 'email',
            value: `${contact.firstname.toLowerCase()}.${contact.surname.toLowerCase()}@email.com`
          }
        ]
      });

      // Add primary address
      await prisma.studentContactAddress.create({
        data: {
          contactId: createdContact.id,
          addressLine1: `${Math.floor(Math.random() * 999) + 1} ${['Oak', 'Elm', 'Pine', 'Maple'][Math.floor(Math.random() * 4)]} Street`,
          city: ['Brighton', 'Hove', 'Worthing'][Math.floor(Math.random() * 3)],
          postcode: `BN${Math.floor(Math.random() * 9) + 1} ${Math.floor(Math.random() * 9) + 1}${String.fromCharCode(65 + Math.floor(Math.random() * 26))}${String.fromCharCode(65 + Math.floor(Math.random() * 26))}`,
          validFrom: new Date('2023-01-01'),
          isPrimary: true,
          updatedBy: firstStaff?.id
        }
      });
    }
  }

  // Create sample transport operators
  const sampleOperators = [
    {
      name: 'Brighton Transport Services',
      phone: '01273 123456',
      email: '<EMAIL>'
    },
    {
      name: 'Sussex School Travel',
      phone: '01273 654321',
      email: '<EMAIL>'
    },
    {
      name: 'Coastal Coaches',
      phone: '01273 987654',
      email: '<EMAIL>'
    }
  ];

  for (const operator of sampleOperators) {
    const existingOperator = await prisma.transportOperator.findFirst({
      where: { name: operator.name }
    });

    if (!existingOperator) {
      await prisma.transportOperator.create({
        data: operator
      });
    }
  }

  // Create sample operator contracts
  const operators = await prisma.transportOperator.findMany();
  const sampleContracts = [
    {
      operatorId: operators[0]?.id,
      code: 'BTS-001',
      name: 'Brighton to Varndean AM Run',
      maxStudents: 16,
      validFrom: new Date('2024-09-01'),
      validTo: new Date('2025-07-31')
    },
    {
      operatorId: operators[0]?.id,
      code: 'BTS-002',
      name: 'Brighton to Varndean PM Run',
      maxStudents: 16,
      validFrom: new Date('2024-09-01'),
      validTo: new Date('2025-07-31')
    },
    {
      operatorId: operators[1]?.id,
      code: 'SST-100',
      name: 'Sussex Special Needs Route',
      maxStudents: 8,
      validFrom: new Date('2024-09-01'),
      validTo: new Date('2025-07-31')
    },
    {
      operatorId: operators[2]?.id,
      code: 'CC-050',
      name: 'Coastal Primary Route',
      maxStudents: 12,
      validFrom: new Date('2024-09-01'),
      validTo: new Date('2025-07-31')
    }
  ];

  for (const contract of sampleContracts) {
    if (contract.operatorId) {
      const existingContract = await prisma.operatorContract.findFirst({
        where: { code: contract.code }
      });

      if (!existingContract) {
        await prisma.operatorContract.create({
          data: contract
        });
      }
    }
  }

  // Create incident types
  const incidentTypes = [
    { code: 'VEHICLE', name: 'Vehicle Issues' },
    { code: 'DELAY', name: 'Delays' },
    { code: 'BEHAVIOR', name: 'Behavioral Issues' },
    { code: 'SAFETY', name: 'Safety Concerns' },
    { code: 'ROUTE', name: 'Route Changes' },
    { code: 'WEATHER', name: 'Weather Related' }
  ];

  for (const incidentType of incidentTypes) {
    await prisma.incidentType.upsert({
      where: { code: incidentType.code },
      update: { name: incidentType.name },
      create: incidentType
    });
  }

  // Create sample incidents
  const contracts = await prisma.operatorContract.findMany();
  const staff = await prisma.staff.findMany();
  const incidentTypesData = await prisma.incidentType.findMany();

  if (contracts.length > 0 && staff.length > 0 && incidentTypesData.length > 0) {
    const sampleIncidents = [
      {
        contractId: contracts[0]?.id,
        incidentTypeId: incidentTypesData.find(t => t.code === 'VEHICLE')?.id,
        severity: 'Critical',
        title: 'Bus Breakdown on Route',
        description: 'The main bus broke down on the morning route, causing delays for 12 students. Alternative transport was arranged.',
        reportedBy: staff[0]?.id,
        occurredAt: new Date('2024-12-15T08:30:00')
      },
      {
        contractId: contracts[0]?.id,
        incidentTypeId: incidentTypesData.find(t => t.code === 'DELAY')?.id,
        severity: 'Moderate',
        title: 'Traffic Delay',
        description: 'Heavy traffic caused a 15-minute delay on the afternoon route.',
        reportedBy: staff[1]?.id,
        occurredAt: new Date('2024-12-14T15:45:00')
      },
      {
        contractId: contracts[1]?.id,
        incidentTypeId: incidentTypesData.find(t => t.code === 'BEHAVIOR')?.id,
        severity: 'Moderate',
        title: 'Student Behavioral Issue',
        description: 'Minor behavioral incident involving two students. Issue was resolved by the driver.',
        reportedBy: staff[0]?.id,
        occurredAt: new Date('2024-12-13T16:20:00')
      }
    ];

    for (const incident of sampleIncidents) {
      if (incident.contractId && incident.incidentTypeId && incident.reportedBy) {
        const existingIncident = await prisma.contractIncident.findFirst({
          where: {
            title: incident.title,
            contractId: incident.contractId
          }
        });

        if (!existingIncident) {
          await prisma.contractIncident.create({
            data: incident
          });
        }
      }
    }
  }

  // Seed Budget Code Rates
  const budgetCodeRates = [
    {
      code: 'ELA001',
      validFrom: new Date('2024-09-01'),
      validTo: null,
      rate: 44.88,
      reason: 'Initial rate setup',
      createdBy: 1
    },
    {
      code: 'ELA002',
      validFrom: new Date('2024-09-01'),
      validTo: null,
      rate: 52.50,
      reason: 'Initial rate setup',
      createdBy: 1
    },
    {
      code: 'ELA003',
      validFrom: new Date('2024-09-01'),
      validTo: null,
      rate: 38.75,
      reason: 'Initial rate setup',
      createdBy: 1
    },
    {
      code: 'ELA004',
      validFrom: new Date('2024-09-01'),
      validTo: null,
      rate: 48.25,
      reason: 'Initial rate setup',
      createdBy: 1
    },
    {
      code: 'ELA005',
      validFrom: new Date('2024-09-01'),
      validTo: null,
      rate: 41.50,
      reason: 'Initial rate setup',
      createdBy: 1
    }
  ];

  for (const rateData of budgetCodeRates) {
    const existingRate = await prisma.budgetCodeRate.findFirst({
      where: {
        code: rateData.code,
        validFrom: rateData.validFrom
      }
    });

    if (!existingRate) {
      await prisma.budgetCodeRate.create({
        data: rateData
      });
    }
  }

  // Seed Operator Contract Budget Assignments
  const contractAssignments = [
    {
      contractId: 1,
      budgetCode: 'ELA001',
      validFrom: new Date('2024-09-01'),
      validTo: null,
      createdBy: 1
    },
    {
      contractId: 2,
      budgetCode: 'ELA002',
      validFrom: new Date('2024-09-01'),
      validTo: null,
      createdBy: 1
    }
  ];

  for (const assignment of contractAssignments) {
    const existingAssignment = await prisma.operatorContractBudgetAssignment.findFirst({
      where: {
        contractId: assignment.contractId,
        budgetCode: assignment.budgetCode,
        validFrom: assignment.validFrom
      }
    });

    if (!existingAssignment) {
      await prisma.operatorContractBudgetAssignment.create({
        data: assignment
      });
    }
  }

  console.log(`Database has been seeded with predefined data.`);
  console.log(`- ${clientTypes.length} client types`);
  console.log(`- ${medicalNeeds.length} medical needs`);
  console.log(`- ${yearGroups.length} year groups`);
  console.log(`- ${eligibilityCodes.length} eligibility codes`);
  console.log(`- ${budgetCodes.length} budget codes`);
  console.log(`- ${budgetCodeRates.length} budget code rates`);
  console.log(`- ${contractAssignments.length} contract budget assignments`);
  console.log(`- ${fundingCodes.length} funding codes`);
  console.log(`- ${sampleStaff.length} staff members`);
  console.log(`- ${sampleEstablishments.length} establishments`);
  console.log(`- ${sampleStudents.length} students`);
  console.log(`- ${sampleContacts.length} contacts with methods and addresses`);
  console.log(`- ${sampleOperators.length} transport operators`);
  console.log(`- ${sampleContracts.length} operator contracts`);
  console.log(`- ${incidentTypes.length} incident types`);
  console.log(`- 3 sample incidents`);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });