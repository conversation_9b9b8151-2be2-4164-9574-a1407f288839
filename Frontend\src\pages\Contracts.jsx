import { useEffect, useState } from 'react';
import { NavLink, Outlet, useLocation, useNavigate } from 'react-router-dom';
import Sidebar from '../components/Sidebar';
import { getCookie } from '../utils/helperFunctions';

export default function Contracts() {
  const [selectedOperator, setSelectedOperator] = useState(null);
  const [selectedContract, setSelectedContract] = useState(null);
  const navigate = useNavigate();
  const location = useLocation();

  // Determine current view based on route
  const getCurrentView = () => {
    const path = location.pathname;
    if (path.includes('/incidents')) return 'incidents';
    if (path.includes('/finances')) return 'finances';
    if (path.includes('/costs')) return 'costs';
    return 'general';
  };

  const currentView = getCurrentView();

  const viewOptions = [
    { name: "General", path: "/contracts" },
    { name: "Finances", path: "/contracts/finances" },
    { name: "Costs", path: "/contracts/costs" },
    { name: "Incidents", path: "/contracts/incidents" },
    { name: "Clients", path: "/contracts/clients" }
  ];

  const handleAddNewContract = () => {
    navigate('/contracts/add-new-contract');
  };

  const handleAddNewOperator = () => {
    navigate('/contracts/add-new-operator');
  };

  useEffect(() => {
    if (!getCookie('TMS_clientToken'))
      navigate('/login');
  });

  return (
    <div className="flex flex-col h-full">
      {/* Navigation tabs (Full width) */}
      <div className="bg-white border-b border-black/[0.1] px-2 w-full">
        <div className="flex justify-start overflow-x-auto">
          {viewOptions.map((option, index) => (
            <NavLink
              key={index}
              to={option.path}
              end={option.path === "/contracts"}
              className={({ isActive }) =>
                `mx-4 py-3 border-b-2 text-sm font-semibold sm:text-base whitespace-nowrap ${
                  isActive
                    ? 'border-purple-600 text-purple-600 font-medium'
                    : 'border-transparent text-gray-600 hover:border-gray-300 hover:text-gray-800'
                }`
              }
            >
              {option.name}
            </NavLink>
          ))}
        </div>
      </div>

      {/* Main content area with sidebar + page view */}
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar */}
        <Sidebar
          selectedOperator={selectedOperator}
          setSelectedOperator={setSelectedOperator}
          selectedContract={selectedContract}
          setSelectedContract={setSelectedContract}
          currentView={currentView}
          onAddNewButtonClicked={handleAddNewContract}
        />

        {/* View content */}
        <div className="flex-1 p-6 bg-gray-50 overflow-auto">
          <Outlet context={{
            selectedOperator,
            setSelectedOperator,
            selectedContract,
            setSelectedContract
          }} />
        </div>
      </div>
    </div>
  );
}
