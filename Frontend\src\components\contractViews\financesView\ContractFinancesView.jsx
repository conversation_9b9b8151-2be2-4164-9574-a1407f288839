import { useEffect, useState } from 'react';
import { useOutletContext } from 'react-router-dom';
import { getCookie } from '../../../utils/helperFunctions';
import AuditLogPanel from '../incidentsView/AuditLogPanel';
import BudgetCodeAssignment from './BudgetCodeAssignment';
import BudgetCodeReference from './BudgetCodeReference';

export default function ContractFinancesView() {
  const { selectedContract, selectedOperator } = useOutletContext();
  const [budgetCodes, setBudgetCodes] = useState([]);
  const [budgetHistory, setBudgetHistory] = useState([]);
  const [contractAssignment, setContractAssignment] = useState(null);
  const [contractHistory, setContractHistory] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // Fetch budget codes
  useEffect(() => {
    fetchBudgetCodes();
    fetchBudgetHistory();
  }, []);

  // Fetch contract assignment when contract changes
  useEffect(() => {
    if (selectedContract?.id) {
      fetchContractAssignment();
      fetchContractHistory();
    }
  }, [selectedContract?.id]);

  const fetchBudgetCodes = async () => {
    try {
      const token = getCookie('TMS_clientToken');
      const response = await fetch(
        `${import.meta.env.VITE_BACKEND_URL}/finances/budget-codes`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      const data = await response.json();
      if (response.ok && data.success) {
        setBudgetCodes(data.data);
      } else {
        setError(data.message || 'Failed to fetch budget codes');
      }
    } catch (error) {
      console.error('Error fetching budget codes:', error);
      setError('Network error occurred');
    }
  };

  const fetchBudgetHistory = async () => {
    try {
      const token = getCookie('TMS_clientToken');
      const response = await fetch(
        `${import.meta.env.VITE_BACKEND_URL}/finances/budget-codes/history`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      const data = await response.json();
      if (response.ok && data.success) {
        setBudgetHistory(data.data);
      }
    } catch (error) {
      console.error('Error fetching budget history:', error);
    }
  };

  const fetchContractAssignment = async () => {
    try {
      const token = getCookie('TMS_clientToken');
      const response = await fetch(
        `${import.meta.env.VITE_BACKEND_URL}/finances/contracts/${selectedContract.id}/budget-assignment`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      const data = await response.json();
      if (response.ok && data.success) {
        setContractAssignment(data.data);
      }
    } catch (error) {
      console.error('Error fetching contract assignment:', error);
    }
  };

  const fetchContractHistory = async () => {
    try {
      const token = getCookie('TMS_clientToken');
      const response = await fetch(
        `${import.meta.env.VITE_BACKEND_URL}/finances/contracts/${selectedContract.id}/budget-history`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      const data = await response.json();
      if (response.ok && data.success) {
        setContractHistory(data.data);
      }
    } catch (error) {
      console.error('Error fetching contract history:', error);
    }
  };

  const handleBudgetCodeUpdate = () => {
    fetchBudgetCodes();
    fetchBudgetHistory();
  };

  const handleAssignmentUpdate = () => {
    fetchContractAssignment();
    fetchContractHistory();
  };

  if (!selectedContract || !selectedOperator) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-gray-400 text-4xl mb-4">📋</div>
          <p className="text-gray-500">Please select a contract to view finances</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Audit Log Panel */}
      <AuditLogPanel contractId={selectedContract?.id} />

      {/* Error Display */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {/* Budget Code Reference */}
      <BudgetCodeReference
        budgetCodes={budgetCodes}
        budgetHistory={budgetHistory}
        onUpdate={handleBudgetCodeUpdate}
      />

      {/* Budget Code Assignment */}
      <BudgetCodeAssignment
        contractId={selectedContract?.id}
        budgetCodes={budgetCodes}
        assignment={contractAssignment}
        history={contractHistory}
        onUpdate={handleAssignmentUpdate}
      />
    </div>
  );
}
