export default function DailyRateBreakdown({ assignment }) {
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(amount);
  };

  const getCurrentRate = () => {
    if (!assignment?.budget?.rates || assignment.budget.rates.length === 0) return null;

    // Find the current active rate (the one with validTo = null or validTo >= today)
    const today = new Date();
    const activeRate = assignment.budget.rates.find(rate => {
      const validFrom = new Date(rate.validFrom);
      const validTo = rate.validTo ? new Date(rate.validTo) : null;
      return validFrom <= today && (!validTo || validTo >= today);
    });

    // If no active rate found, return the most recent one
    return activeRate || assignment.budget.rates[0];
  };

  const getDailyBreakdown = () => {
    const currentRate = getCurrentRate();
    if (!currentRate) return [];

    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
    
    return days.map(day => ({
      day,
      rate: currentRate.rate,
      budgetCode: assignment.budgetCode,
      budgetName: assignment.budget?.name || ''
    }));
  };

  const currentRate = getCurrentRate();
  const dailyBreakdown = getDailyBreakdown();

  if (!currentRate) {
    return (
      <div className="bg-white rounded-md shadow-sm p-6">
        <div className="text-center">
          <div className="text-gray-400 text-3xl mb-2">📅</div>
          <p className="text-gray-500">No rate information available for daily breakdown</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-md shadow-sm overflow-hidden">
      <div className="px-4 py-3 bg-white border-b border-black/[0.1]">
        <h3 className="font-medium text-gray-700">Daily Rate Breakdown</h3>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Day
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Rate
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Budget Code
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {dailyBreakdown.map((item, index) => (
              <tr key={item.day} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {item.day}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {formatCurrency(item.rate)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {item.budgetCode}
                    </div>
                    <div className="text-sm text-gray-500">
                      ({item.budgetName})
                    </div>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Summary */}
      <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium text-gray-700">
            Total Weekly Rate (5 days):
          </span>
          <span className="text-lg font-bold text-gray-900">
            {formatCurrency(currentRate.rate * 5)}
          </span>
        </div>
        <div className="mt-2 text-xs text-gray-500">
          Based on current active rate from {assignment.budgetCode}
        </div>
      </div>
    </div>
  );
}
