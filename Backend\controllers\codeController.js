const { PrismaClient } = require('../generated/prisma/client');
const prisma = new PrismaClient();

// Get all code assignments for a student
const getAllCodeAssignments = async (req, res) => {
  try {
    const { studentId } = req.params;
    const studentIdInt = parseInt(studentId);

    if (!studentIdInt) {
      return res.status(400).json({
        success: false,
        message: 'Invalid student ID'
      });
    }

    // Get all code assignments for the student with code details
    const assignments = await prisma.studentCodeAssignment.findMany({
      where: {
        studentId: studentIdInt
      },
      include: {
        eligibility: {
          select: {
            code: true,
            name: true,
            description: true
          }
        },
        budget: {
          select: {
            code: true,
            name: true,
            description: true
          }
        },
        funding: {
          select: {
            code: true,
            name: true,
            description: true
          }
        },
        createdByStaff: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        },
        updatedByStaff: {
          select: {
            id: true,
            firstName: true,
            lastName: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Get all available codes for dropdowns
    const [eligibilityCodes, budgetCodes, fundingCodes] = await Promise.all([
      prisma.eligibilityCode.findMany({
        orderBy: { code: 'asc' }
      }),
      prisma.budgetCode.findMany({
        orderBy: { code: 'asc' }
      }),
      prisma.fundingCode.findMany({
        orderBy: { code: 'asc' }
      })
    ]);

    return res.status(200).json({
      success: true,
      data: {
        assignments,
        codes: {
          eligibility: eligibilityCodes,
          budget: budgetCodes,
          funding: fundingCodes
        }
      }
    });

  } catch (error) {
    console.error('Error fetching code assignments:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

// Add new code assignment for a student
const addNewCodeAssignment = async (req, res) => {
  try {
    const { studentId } = req.params;
    const { eligibility_code, budget_code, funding_code, valid_from, valid_to } = req.body;
    const studentIdInt = parseInt(studentId);
    const staffId = req.staff?.id; // Assuming staff ID comes from auth middleware

    if (!studentIdInt) {
      return res.status(400).json({
        success: false,
        message: 'Invalid student ID'
      });
    }

    if (!eligibility_code || !budget_code || !funding_code || !valid_from) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields'
      });
    }

    // Start transaction
    const result = await prisma.$transaction(async (tx) => {
      // End current active assignment (set valid_to to current date)
      await tx.studentCodeAssignment.updateMany({
        where: {
          studentId: studentIdInt,
          validTo: null
        },
        data: {
          validTo: new Date(),
          updatedBy: staffId,
          updatedAt: new Date()
        }
      });

      // Create new assignment
      const newAssignment = await tx.studentCodeAssignment.create({
        data: {
          studentId: studentIdInt,
          eligibilityCode: eligibility_code,
          budgetCode: budget_code,
          fundingCode: funding_code,
          validFrom: new Date(valid_from),
          validTo: valid_to ? new Date(valid_to) : null,
          createdBy: staffId,
          createdAt: new Date()
        },
        include: {
          eligibility: {
            select: {
              code: true,
              name: true,
              description: true
            }
          },
          budget: {
            select: {
              code: true,
              name: true,
              description: true
            }
          },
          funding: {
            select: {
              code: true,
              name: true,
              description: true
            }
          },
          createdByStaff: {
            select: {
              id: true,
              firstName: true,
              lastName: true
            }
          }
        }
      });

      return newAssignment;
    });

    return res.status(201).json({
      success: true,
      data: result,
      message: 'Code assignment created successfully'
    });

  } catch (error) {
    console.error('Error creating code assignment:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

module.exports = {
  getAllCodeAssignments,
  addNewCodeAssignment
};