export default function CurrentActiveRate({ assignment }) {
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-GB');
  };

  const getCurrentRate = () => {
    if (!assignment?.budget?.rates || assignment.budget.rates.length === 0) return null;

    // Find the current active rate (the one with validTo = null or validTo >= today)
    const today = new Date();
    const activeRate = assignment.budget.rates.find(rate => {
      const validFrom = new Date(rate.validFrom);
      const validTo = rate.validTo ? new Date(rate.validTo) : null;
      return validFrom <= today && (!validTo || validTo >= today);
    });

    // If no active rate found, return the most recent one
    return activeRate || assignment.budget.rates[0];
  };

  const getWeeklyRate = () => {
    const currentRate = getCurrentRate();
    if (!currentRate) return null;
    return currentRate.rate * 5; // 5 school days in a week
  };

  const currentRate = getCurrentRate();
  const weeklyRate = getWeeklyRate();

  if (!currentRate) {
    return (
      <div className="bg-white rounded-md shadow-sm p-6">
        <div className="text-center">
          <div className="text-gray-400 text-3xl mb-2">💰</div>
          <p className="text-gray-500">No active rate found for this contract</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-md shadow-sm overflow-hidden">
      <div className="px-4 py-3 bg-white border-b border-black/[0.1]">
        <h3 className="font-medium text-gray-700">Current Active Rate</h3>
      </div>

      <div className="p-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div>
              <p className="text-sm text-gray-600">
                Valid from {formatDate(assignment.validFrom)} to {assignment.validTo ? formatDate(assignment.validTo) : 'ongoing'}
              </p>
              <p className="text-xs text-gray-500">
                Last updated: {currentRate.reason || 'Initial rate setup'}
              </p>
            </div>
          </div>
          
          <div className="text-right">
            <div className="text-3xl font-bold text-gray-900">
              {formatCurrency(weeklyRate)}
            </div>
            <div className="text-sm text-gray-600">Weekly Rate</div>
          </div>
        </div>

        {/* Additional Rate Information */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t border-gray-200">
          <div>
            <label className="block text-sm text-gray-500 mb-1">Budget Code</label>
            <p className="font-medium text-gray-900">{assignment.budgetCode}</p>
            <p className="text-sm text-gray-600">{assignment.budget?.name}</p>
          </div>
          
          <div>
            <label className="block text-sm text-gray-500 mb-1">Daily Rate</label>
            <p className="font-medium text-gray-900">{formatCurrency(currentRate.rate)}</p>
          </div>
          
          <div>
            <label className="block text-sm text-gray-500 mb-1">Rate Period</label>
            <p className="font-medium text-gray-900">
              {formatDate(currentRate.validFrom)} - {currentRate.validTo ? formatDate(currentRate.validTo) : 'Current'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
