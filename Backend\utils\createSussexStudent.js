const { PrismaClient } = require('../generated/prisma/client');
const prisma = new PrismaClient();

async function createSussexStudent() {
  console.log('Creating student with ELA004 budget code for Sussex contract...');

  try {
    // Create a new student
    const student = await prisma.student.create({
      data: {
        title: 'Mr',
        firstName: '<PERSON>',
        surname: '<PERSON>',
        knownAs: '<PERSON><PERSON>',
        gender: 'M',
        dateOfBirth: new Date('2012-04-20'),
        clientTypeId: 2, // SEN Transport
        requiresTravel: true,
        inCare: true, // LAC student
        yearGroupId: 1, // YR_7
        estabId: 1,
        isActive: true
      }
    });

    console.log(`Created student: ${student.firstName} ${student.surname} (ID: ${student.id})`);

    // Create code assignment with ELA004 budget code (matches Sussex contract)
    await prisma.studentCodeAssignment.create({
      data: {
        studentId: student.id,
        eligibilityCode: 'E001', // Statutory Distance
        budgetCode: 'ELA004', // LAC Funded Travel - matches Sussex contract
        fundingCode: 'LA_HTS', // LA Funded Home-to-School
        validFrom: new Date('2024-09-01'),
        validTo: null,
        createdBy: 1
      }
    });

    console.log(`Created code assignment for ${student.firstName} with budget code ELA004`);

    // Create another student
    const student2 = await prisma.student.create({
      data: {
        title: 'Miss',
        firstName: 'Emily',
        surname: 'Sussex',
        knownAs: 'Em',
        gender: 'F',
        dateOfBirth: new Date('2011-11-08'),
        clientTypeId: 2, // SEN Transport
        requiresTravel: true,
        inCare: true, // LAC student
        yearGroupId: 2, // YR_8
        estabId: 1,
        isActive: true
      }
    });

    console.log(`Created student: ${student2.firstName} ${student2.surname} (ID: ${student2.id})`);

    // Create code assignment with ELA004 budget code
    await prisma.studentCodeAssignment.create({
      data: {
        studentId: student2.id,
        eligibilityCode: 'E001', // Statutory Distance
        budgetCode: 'ELA004', // LAC Funded Travel - matches Sussex contract
        fundingCode: 'LA_HTS', // LA Funded Home-to-School
        validFrom: new Date('2024-09-01'),
        validTo: null,
        createdBy: 1
      }
    });

    console.log(`Created code assignment for ${student2.firstName} with budget code ELA004`);

    console.log('✅ Sussex students created successfully!');
  } catch (error) {
    console.error('Error creating Sussex students:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

createSussexStudent()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });
