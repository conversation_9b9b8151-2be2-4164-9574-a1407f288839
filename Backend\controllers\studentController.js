// /controllers/studentController.js
const { PrismaClient } = require('../generated/prisma/client');
const prisma = new PrismaClient();

// Get full student information
const getStudentInfo = async (req, res) => {
  try {
    const studentId = parseInt(req.params.studentId);
    
    if (!studentId) {
      return res.status(400).json({
        success: false,
        message: 'Student ID is required'
      });
    }

    // Fetch comprehensive student information
    const student = await prisma.student.findUnique({
      where: {
        id: studentId
      },
      include: {
        clientType: true,
        medicalNeed: true,
        yearGroup: true,
        establishment: {
          select: {
            id: true,
            name: true,
            addressLine1: true,
            city: true,
            postcode: true,
            telephone: true,
            email: true
          }
        },
        contacts: {
          where: {
            isPrimary: true
          },
          include: {
            contactMethods: true,
            addresses: {
              orderBy: {
                validFrom: 'desc'
              }
            },
            notes: {
              include: {
                author: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true
                  }
                }
              },
              orderBy: {
                createdAt: 'desc'
              }
            }
          }
        },
        travelAssignments: {
          include: {
            operatorContract: {
              include: {
                operator: true
              }
            },
            termPeriod: true
          }
        },
        communications: {
          include: {
            author: {
              select: {
                id: true,
                firstName: true,
                lastName: true
              }
            }
          },
          orderBy: {
            sentAt: 'desc'
          }
        }
      }
    });

    if (!student) {
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      });
    }

    // Format student age
    let age = null;
    if (student.dateOfBirth) {
      const birthDate = new Date(student.dateOfBirth);
      const today = new Date();
      age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
    }

    // Organize primary contact information
    const primaryContact = student.contacts.length > 0 ? student.contacts[0] : null;
    let contactInfo = null;
    let addresses = [];
    let notes = [];
    
    if (primaryContact) {
      // Get contact methods
      const email = primaryContact.contactMethods.find(m => m.methodType === 'EMAIL')?.value || null;
      const phone = primaryContact.contactMethods.find(m => m.methodType === 'PHONE')?.value || null;
      
      contactInfo = {
        id: primaryContact.id,
        name: `${primaryContact.title || ''} ${primaryContact.firstname} ${primaryContact.surname}`.trim(),
        relationship: primaryContact.relationship,
        email,
        phone
      };
      
      // Format addresses
      addresses = primaryContact.addresses.map(addr => ({
        id: addr.id,
        address: `${addr.addressLine1}${addr.addressLine2 ? ', ' + addr.addressLine2 : ''}`,
        city: addr.city,
        county: addr.county,
        postcode: addr.postcode,
        validFrom: addr.validFrom,
        validTo: addr.validTo,
        isPrimary: addr.isPrimary
      }));
      
      // Format notes
      notes = primaryContact.notes.map(note => ({
        id: note.id,
        content: note.content,
        type: note.noteType,
        createdAt: note.createdAt,
        author: note.author ? `${note.author.firstName} ${note.author.lastName}` : 'Unknown'
      }));
    }
    
    // Format transport operator information
    let transportInfo = null;
    if (student.travelAssignments && student.travelAssignments.length > 0) {
      const latestAssignment = student.travelAssignments[0];
      if (latestAssignment.operatorContract?.operator) {
        const operator = latestAssignment.operatorContract.operator;
        transportInfo = {
          operatorId: operator.id,
          name: operator.name,
          phone: operator.phone,
          routeCode: latestAssignment.operatorContract.code,
          runType: latestAssignment.runType,
          maxTravelTime: latestAssignment.maxTravelTimeMins
        };
      }
    }
    
    // Format communications
    const communications = student.communications.map(comm => ({
      id: comm.id,
      type: comm.commType,
      group: comm.commGroup,
      subject: comm.subject,
      content: comm.content,
      sentAt: comm.sentAt,
      author: comm.author ? `${comm.author.firstName} ${comm.author.lastName}` : 'Unknown'
    }));

    // Prepare response object
    const formattedStudent = {
      basicInfo: {
        id: student.id,
        title: student.title,
        firstName: student.firstName,
        surname: student.surname,
        knownAs: student.knownAs,
        fullName: `${student.firstName} ${student.surname}`,
        gender: student.gender,
        dateOfBirth: student.dateOfBirth,
        age,
        clientTypeId: student.clientType?.id,
        clientType: student.clientType?.code || null,
        requiresTravel: student.requiresTravel,
        inCare: student.inCare,
        medicalNeedId: student.medicalNeed?.id,
        medicalNeed: student.medicalNeed?.description || null,
        yearGroupId: student.yearGroupId,
        yearGroup: student.yearGroup?.name || null,
        isActive: student.isActive
      },
      school: student.establishment ? {
        id: student.establishment.id,
        name: student.establishment.name,
        address: student.establishment.addressLine1,
        city: student.establishment.city,
        postcode: student.establishment.postcode,
        telephone: student.establishment.telephone,
        email: student.establishment.email
      } : null,
      primaryContact: contactInfo,
      addresses,
      transportInfo,
      notes,
      communications
    };

    res.status(200).json({
      success: true,
      data: formattedStudent
    });
  } catch (error) {
    console.error('Get student info error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Search students by name
const searchStudents = async (req, res) => {
  try {
    const { search } = req.query;
    
    if (!search || search.length < 2) {
      return res.status(400).json({
        success: false,
        message: 'Search must be at least 2 characters'
      });
    }

    // Search for students by name
    const students = await prisma.student.findMany({
      where: {
        OR: [
          { firstName: { contains: search, mode: 'insensitive' } },
          { surname: { contains: search, mode: 'insensitive' } },
          { knownAs: { contains: search, mode: 'insensitive' } }
        ]
      },
      select: {
        id: true,
        firstName: true,
        surname: true,
        knownAs: true,
        dateOfBirth: true
      },
      take: 10, // Limit to 10 results
      orderBy: {
        surname: 'asc'
      }
    });

    const formattedResults = students.map(student => ({
      id: student.id,
      name: `${student.firstName} ${student.surname}`,
      knownAs: student.knownAs,
      dateOfBirth: student.dateOfBirth
    }));

    res.status(200).json({
      success: true,
      data: formattedResults
    });
  } catch (error) {
    console.error('Search students error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Get all students with essential information
const getAllStudents = async (req, res) => {
  try {
    const students = await prisma.student.findMany({
      select: {
        id: true,
        title: true,
        firstName: true,
        surname: true,
        knownAs: true,
        gender: true,
        dateOfBirth: true,
        isActive: true,
        yearGroup: {
          select: {
            id: true,
            code: true,
            name: true
          }
        },
        establishment: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: [
        { isActive: 'desc' },  // Active students first
        { surname: 'asc' },    // Then sorted by surname
        { firstName: 'asc' }   // Then by first name
      ]
    });

    const formattedResults = students.map(student => ({
      id: student.id,
      firstName: student.firstName,
      surname: student.surname,
      fullName: `${student.firstName} ${student.surname}`,
      knownAs: student.knownAs,
      establishmentId: student.establishment?.id || null,
      establishmentName: student.establishment?.name || null,
      yearGroup: student.yearGroup ? {
        id: student.yearGroup.id,
        code: student.yearGroup.code,
        name: student.yearGroup.name
      } : null,
      status: student.isActive ? 'Active' : 'Inactive'
    }));

    res.status(200).json({
      success: true,
      data: formattedResults
    });
  } catch (error) {
    console.error('Get all students error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Add a new student
const addNewStudent = async (req, res) => {
  try {
    const {
      // Student details
      title,
      firstName,
      surname,
      knownAs,
      gender,
      dateOfBirth,
      clientTypeId,
      requiresTravel,
      inCare,
      medicalNeedId,
      yearGroupId,
      estabId,
      isActive = true,
      // Contact details
      contact
    } = req.body;

    // Validate required fields
    if (!firstName || !surname) {
      return res.status(400).json({
        success: false,
        message: 'First name and surname are required'
      });
    }

    // Parse dateOfBirth if provided
    let parsedDateOfBirth = null;
    if (dateOfBirth) {
      parsedDateOfBirth = new Date(dateOfBirth);
      if (isNaN(parsedDateOfBirth.getTime())) {
        return res.status(400).json({
          success: false,
          message: 'Invalid date format for date of birth'
        });
      }
    }

    // Parse contact dateOfBirth if provided
    let parsedContactDateOfBirth = null;
    if (contact && contact.dateOfBirth) {
      parsedContactDateOfBirth = new Date(contact.dateOfBirth);
      if (isNaN(parsedContactDateOfBirth.getTime())) {
        return res.status(400).json({
          success: false,
          message: 'Invalid date format for contact date of birth'
        });
      }
    }

    // Create transaction to ensure student and contact data are saved together
    const result = await prisma.$transaction(async (prisma) => {
      // Create new student
      const newStudent = await prisma.student.create({
        data: {
          title: title || null,
          firstName,
          surname,
          knownAs: knownAs || null,
          gender: gender || null,
          dateOfBirth: parsedDateOfBirth,
          clientTypeId: clientTypeId ? parseInt(clientTypeId) : null,
          requiresTravel: requiresTravel === true,
          inCare: inCare === true,
          medicalNeedId: medicalNeedId ? parseInt(medicalNeedId) : null,
          yearGroupId: parseInt(yearGroupId) || null,
          estabId: estabId ? parseInt(estabId) : null,
          isActive
        }
      });

      // Create contact if provided
      if (contact && (contact.firstname || contact.surname)) {
        // Validate contact data
        if (contact.firstname && !contact.surname) {
          throw new Error('Contact surname is required if firstname is provided');
        }

        // Create the contact
        const newContact = await prisma.studentContact.create({
          data: {
            studentId: newStudent.id,
            title: contact.title || null,
            firstname: contact.firstname,
            surname: contact.surname,
            knownAs: contact.knownAs || null,
            relationship: contact.relationship || null,
            dateOfBirth: parsedContactDateOfBirth,
            isPrimary: contact.isPrimary || true,
            isPayer: contact.isPayer || false
          }
        });

        // Create contact methods if provided
        if (contact.contactMethods && contact.contactMethods.length > 0) {
          for (const method of contact.contactMethods) {
            if (method.value) {
              await prisma.studentContactMethod.create({
                data: {
                  contactId: newContact.id,
                  methodType: method.methodType,
                  value: method.value
                }
              });
            }
          }
        }

        // Create address if provided
        if (contact.address) {
          const addressProvided = contact.address.addressLine1 || 
                                  contact.address.city || 
                                  contact.address.postcode;
          
          if (addressProvided) {
            // Validate address data
            if (!contact.address.addressLine1 || !contact.address.city || !contact.address.postcode) {
              throw new Error('Address Line 1, City and Postcode are required if adding an address');
            }

            await prisma.studentContactAddress.create({
              data: {
                contactId: newContact.id,
                addressLine1: contact.address.addressLine1,
                addressLine2: contact.address.addressLine2 || null,
                city: contact.address.city,
                county: contact.address.county || null,
                postcode: contact.address.postcode,
                validFrom: new Date(), // Set to current date
                isPrimary: contact.address.isPrimary || true
              }
            });
          }
        }
      }

      return newStudent;
    });

    res.status(201).json({
      success: true,
      data: result,
      message: 'Student added successfully'
    });
  } catch (error) {
    console.error('Add new student error:', error);
    if (error.message.includes('required')) {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Get student contacts with related data in standardized format
const getStudentContacts = async (req, res) => {
  try {
    const { studentId } = req.params;
    
    // Validate student ID
    if (!studentId || isNaN(parseInt(studentId))) {
      return res.status(400).json({ 
        success: false,
        message: 'Invalid student ID' 
      });
    }

    // Check if student exists
    const student = await prisma.student.findUnique({
      where: { id: parseInt(studentId) }
    });

    if (!student) {
      return res.status(404).json({ 
        success: false,
        message: 'Student not found' 
      });
    }

    // Get all contacts for the student with related data
    const contacts = await prisma.studentContact.findMany({
      where: {
        studentId: parseInt(studentId)
      },
      include: {
        contactMethods: true,
        addresses: {
          orderBy: {
            isPrimary: 'desc'
          }
        },
        notes: {
          include: {
            author: {
              select: {
                id: true,
                firstName: true,
                lastName: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          }
        },
        communications: {
          include: {
            author: {
              select: {
                id: true,
                firstName: true,
                lastName: true
              }
            }
          },
          orderBy: {
            sentAt: 'desc'
          }
        }
      },
      orderBy: [
        { isPrimary: 'desc' },
        { surname: 'asc' },
        { firstname: 'asc' }
      ]
    });

    // Format contacts to match getStudentInfo's primary contact format
    const formattedContacts = contacts.map(contact => {
      // Get contact methods
      const email = contact.contactMethods.find(m => m.methodType === 'EMAIL')?.value || null;
      const phone = contact.contactMethods.find(m => m.methodType === 'PHONE')?.value || null;
      
      // Format addresses
      const addresses = contact.addresses.map(addr => ({
        id: addr.id,
        address: `${addr.addressLine1}${addr.addressLine2 ? ', ' + addr.addressLine2 : ''}`,
        city: addr.city,
        county: addr.county,
        postcode: addr.postcode,
        validFrom: addr.validFrom,
        validTo: addr.validTo,
        isPrimary: addr.isPrimary
      }));
      
      // Format notes
      const notes = contact.notes.map(note => ({
        id: note.id,
        content: note.content,
        type: note.noteType,
        createdAt: note.createdAt,
        author: note.author ? `${note.author.firstName} ${note.author.lastName}` : 'Unknown'
      }));

      // Format communications
      const communications = contact.communications.map(comm => ({
        id: comm.id,
        type: comm.commType,
        group: comm.commGroup,
        subject: comm.subject,
        content: comm.content,
        sentAt: comm.sentAt,
        author: comm.author ? `${comm.author.firstName} ${comm.author.lastName}` : 'Unknown'
      }));

      return {
        id: contact.id,
        title: contact.title,
        firstname: contact.firstname,
        surname: contact.surname,
        knownAs: contact.knownAs,
        name: `${contact.title || ''} ${contact.firstname} ${contact.surname}`.trim(),
        relationship: contact.relationship,
        dateOfBirth: contact.dateOfBirth,
        isPrimary: contact.isPrimary,
        isPayer: contact.isPayer,
        email,
        phone,
        addresses,
        notes,
        communications
      };
    });

    res.status(200).json({
      success: true,
      data: formattedContacts
    });
  } catch (error) {
    console.error('Error fetching student contacts:', error);
    res.status(500).json({ 
      success: false,
      message: 'Failed to fetch student contacts' 
    });
  }
};

// Add a new note for a student's contact
const addNewNote = async (req, res) => {
  try {
    const { studentId } = req.params;
    const { contactId, content, noteType } = req.body;
    const authorId = req.staff.id; // Assuming user ID is attached to request by verifyToken middleware
    
    // Validate input
    if (!studentId || !contactId || !content) {
      return res.status(400).json({ message: 'Student ID, contact ID, and content are required' });
    }

    // Validate IDs are valid numbers
    if (isNaN(parseInt(studentId)) || isNaN(parseInt(contactId))) {
      return res.status(400).json({ message: 'Invalid student or contact ID' });
    }

    // Check if contact belongs to student
    const contact = await prisma.studentContact.findFirst({
      where: {
        id: parseInt(contactId),
        studentId: parseInt(studentId)
      }
    });

    if (!contact) {
      return res.status(404).json({ message: 'Contact not found for this student' });
    }

    // Create new note
    const newNote = await prisma.studentContactNote.create({
      data: {
        contactId: parseInt(contactId),
        authorId: authorId,
        content: content,
        noteType: noteType || 'General',
        createdAt: new Date()
      }
    });

    res.status(201).json(newNote);
  } catch (error) {
    console.error('Error adding new note:', error);
    res.status(500).json({ message: 'Failed to add new note' });
  }
};

// Add a new communication for a student
const addNewCommunication = async (req, res) => {
  try {
    const { studentId } = req.params;
    const { contactId, commType, commGroup, subject, content } = req.body;
    const authorId = req.staff.id; // Assuming user ID is attached to request by verifyToken middleware
    
    // Validate input
    if (!studentId || !contactId || !commType || !commGroup || !subject || !content) {
      return res.status(400).json({ 
        message: 'Student ID, contact ID, communication type, group, subject, and content are required' 
      });
    }

    // Validate IDs are valid numbers
    if (isNaN(parseInt(studentId)) || isNaN(parseInt(contactId))) {
      return res.status(400).json({ message: 'Invalid student or contact ID' });
    }

    // Check if contact belongs to student
    const contact = await prisma.studentContact.findFirst({
      where: {
        id: parseInt(contactId),
        studentId: parseInt(studentId)
      }
    });

    if (!contact) {
      return res.status(404).json({ message: 'Contact not found for this student' });
    }

    // Create new communication
    const newCommunication = await prisma.studentContactCommunication.create({
      data: {
        studentId: parseInt(studentId),
        contactId: parseInt(contactId),
        authorId: authorId,
        commType: commType,
        commGroup: commGroup,
        subject: subject,
        content: content,
        sentAt: new Date()
      }
    });

    res.status(201).json(newCommunication);
  } catch (error) {
    console.error('Error adding new communication:', error);
    res.status(500).json({ message: 'Failed to add new communication' });
  }
};

const updateBasicStudentInfo = async (req, res) => {
  try {
    const id = parseInt(req.params.studentId);
    const {
      title,
      firstName,
      surname,
      knownAs,
      gender,
      dateOfBirth,
      clientTypeId,
      requiresTravel,
      inCare,
      medicalNeedId,
      yearGroupId,
      estabId,
      isActive
    } = req.body;

    // Validate required fields
    if (!firstName || !surname) {
      return res.status(400).json({
        success: false,
        message: 'First name and surname are required'
      });
    }

    // Validate student exists
    const existingStudent = await prisma.student.findUnique({
      where: { id: id }
    });

    if (!existingStudent) {
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      });
    }

    // Parse dateOfBirth if provided
    let parsedDateOfBirth = null;
    if (dateOfBirth) {
      parsedDateOfBirth = new Date(dateOfBirth);
      if (isNaN(parsedDateOfBirth.getTime())) {
        return res.status(400).json({
          success: false,
          message: 'Invalid date format for date of birth'
        });
      }
    }

    // Validate foreign key references exist
    if (clientTypeId) {
      const clientType = await prisma.clientType.findUnique({
        where: { id: parseInt(clientTypeId) }
      });
      
      if (!clientType) {
        return res.status(400).json({
          success: false,
          message: 'Invalid client type selected'
        });
      }
    }

    if (medicalNeedId) {
      const medicalNeed = await prisma.medicalNeed.findUnique({
        where: { id: parseInt(medicalNeedId) }
      });
      
      if (!medicalNeed) {
        return res.status(400).json({
          success: false,
          message: 'Invalid medical need selected'
        });
      }
    }

    if (estabId) {
      const establishment = await prisma.establishment.findUnique({
        where: { id: parseInt(estabId) }
      });
      
      if (!establishment) {
        return res.status(400).json({
          success: false,
          message: 'Invalid establishment selected'
        });
      }
    }

    if (yearGroupId) {
      const yearGroup = await prisma.yearGroup.findUnique({
        where: { id: parseInt(yearGroupId) }
      });

      if (!yearGroup) {
        return res.status(400).json({
          success: false,
          message: 'Invalid year group selected'
        });
      }
    }

    // Prepare update data
    const updateData = {
      title: title || null,
      firstName,
      surname,
      knownAs: knownAs || null,
      gender: gender || null,
      dateOfBirth: parsedDateOfBirth,
      clientTypeId: clientTypeId ? parseInt(clientTypeId) : null,
      requiresTravel: Boolean(requiresTravel),
      inCare: Boolean(inCare),
      medicalNeedId: medicalNeedId ? parseInt(medicalNeedId) : null,
      yearGroupId: yearGroupId ? parseInt(yearGroupId) : null,
      estabId: estabId ? parseInt(estabId) : null,
      updatedAt: new Date() // Update timestamp
    };

    // Only update isActive if it's explicitly provided
    if (isActive !== undefined) {
      updateData.isActive = Boolean(isActive);
    }

    // Create an audit log entry
    const auditLogEntry = {
      entity: 'student',
      entityId: parseInt(id),
      operation: 'UPDATE',
      changedBy: req.staff.id, // Assuming req.user is set by authentication middleware
      changedAt: new Date(),
      oldData: existingStudent,
      newData: { ...updateData }
    };

    // Update student and create audit log in a transaction
    const result = await prisma.$transaction(async (prisma) => {
      // Update the student
      const updatedStudent = await prisma.student.update({
        where: { id: parseInt(id) },
        data: updateData,
        include: {
          clientType: true,
          medicalNeed: true,
          establishment: true
        }
      });

      // Create audit log entry
      await prisma.auditLog.create({
        data: auditLogEntry
      });

      return updatedStudent;
    });

    // Format response data
    const responseData = {
      id: result.id,
      basicInfo: {
        title: result.title,
        firstName: result.firstName,
        surname: result.surname,
        knownAs: result.knownAs,
        gender: result.gender,
        dateOfBirth: result.dateOfBirth,
        clientType: result.clientType,
        requiresTravel: result.requiresTravel,
        inCare: result.inCare,
        medicalNeed: result.medicalNeed,
        yearGroup: result.yearGroup,
        isActive: result.isActive
      },
      school: result.establishment
    };

    res.status(200).json({
      success: true,
      data: responseData,
      message: 'Student information updated successfully'
    });
  } catch (error) {
    console.error('Update student error:', error);
    
    // Handle specific database errors
    if (error.code === 'P2002') {
      return res.status(400).json({
        success: false,
        message: 'A unique constraint violation occurred'
      });
    }
    
    if (error.code === 'P2003') {
      return res.status(400).json({
        success: false,
        message: 'Foreign key constraint failed. Check that all referenced IDs exist'
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Server error updating student information'
    });
  }
};

module.exports = {
  getStudentInfo,
  searchStudents,
  getAllStudents,
  addNewStudent,
  getStudentContacts,
  addNewNote,
  addNewCommunication,
  updateBasicStudentInfo
};