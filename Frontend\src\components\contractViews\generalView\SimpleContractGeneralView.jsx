import { useEffect, useState } from 'react';
import { FaBuilding } from 'react-icons/fa';
import { useNavigate, useOutletContext } from 'react-router-dom';
import { getCookie } from '../../../utils/helperFunctions';
import AuditLogPanel from '../incidentsView/AuditLogPanel';

export default function SimpleContractGeneralView() {
  const context = useOutletContext();
  const { selectedContract, selectedOperator } = context || {};
  const [contractDetails, setContractDetails] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    if (selectedContract) {
      fetchContractDetails(selectedContract.id);
    }
  }, [selectedContract]);

  const fetchContractDetails = async (contractId) => {
    if (!contractId) {
      console.error('No contract ID provided');
      return;
    }

    setIsLoading(true);
    setError('');
    try {
      const token = getCookie('TMS_clientToken');
      console.log('Fetching contract details for ID:', contractId);

      const response = await fetch(`${import.meta.env.VITE_BACKEND_URL}/contracts/${contractId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers.get('content-type'));

      if (!response.ok) {
        // Try to get error message from response
        let errorMessage = 'Failed to load contract details';
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch (jsonError) {
          console.error('Response is not valid JSON:', jsonError);
          errorMessage = `Server error: ${response.status} ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      const data = await response.json();
      console.log('Contract details received:', data);

      if (data.success) {
        setContractDetails(data.data);
      } else {
        setError(data.message || 'Failed to load contract details');
      }
    } catch (err) {
      setError(err.message || 'Failed to load contract details');
      console.error('Error fetching contract details:', err);
    } finally {
      setIsLoading(false);
    }
  };

  if (!selectedContract) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <FaBuilding className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Contract Selected</h3>
          <p className="text-gray-500">
            Search for a transport operator and select a contract to view details
          </p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Loading contract details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="text-red-500 mb-4">⚠️</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Contract</h3>
          <p className="text-red-500">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Recent Activity Panel */}
      <AuditLogPanel contractId={selectedContract?.id} section="general" />

      {/* Header with operator info and status */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
            <FaBuilding className="w-4 h-4 text-purple-600" />
          </div>
          <div>
            <h2 className="text-lg font-semibold text-gray-900">{selectedOperator?.name || 'Brighton Transport Services'}</h2>
            <p className="text-sm text-gray-500">Transport Operator</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          {contractDetails && (
            <>
              <span className="text-sm text-gray-500">
                {contractDetails.currentStudents} / {contractDetails.maxStudents}
              </span>
              <span className="text-sm text-gray-500">
                {contractDetails.maxStudents - contractDetails.currentStudents} spots remaining
              </span>
            </>
          )}
          <span className={`text-xs font-medium px-2 py-1 rounded ${
            contractDetails?.isActive
              ? 'bg-green-100 text-green-800'
              : 'bg-red-100 text-red-800'
          }`}>
            {contractDetails?.isActive ? 'Active' : 'Inactive'}
          </span>
          <button
            className="bg-purple-600 text-white text-sm font-medium px-4 py-2 rounded hover:bg-purple-700 flex items-center gap-2"
            onClick={() => navigate("/contracts/edit-contract-details")}
          >
            <span>✏️</span>
            Edit Details
          </button>
        </div>
      </div>

      {/* Contract Details Card */}
      <div className="bg-white border border-gray-200 rounded-lg">
        <div className="p-6">
          <div className="grid grid-cols-3 gap-6">
            {/* First Row */}
            <div>
              <label className="text-sm text-gray-500">Contract Code</label>
              <p className="font-medium text-gray-900">{contractDetails?.code || '-'}</p>
            </div>
            <div>
              <label className="text-sm text-gray-500">Contract Name</label>
              <p className="font-medium text-gray-900">{contractDetails?.name || '-'}</p>
            </div>
            <div>
              <label className="text-sm text-gray-500">Max Students</label>
              <p className="font-medium text-gray-900">{contractDetails?.maxStudents || '-'}</p>
            </div>

            {/* Second Row */}
            <div>
              <label className="text-sm text-gray-500">Valid From</label>
              <p className="font-medium text-gray-900">
                {contractDetails?.validFrom ? new Date(contractDetails.validFrom).toLocaleDateString('en-GB') : '-'}
              </p>
            </div>
            <div>
              <label className="text-sm text-gray-500">Valid To</label>
              <p className="font-medium text-gray-900">
                {contractDetails?.validTo ? new Date(contractDetails.validTo).toLocaleDateString('en-GB') : '-'}
              </p>
            </div>
            <div>
              <label className="text-sm text-gray-500">Status</label>
              <p className="font-medium text-gray-900">
                {contractDetails?.isActive ? 'Active' : 'Inactive'}
              </p>
            </div>
          </div>
        
        </div>
      </div>
    </div>
  );
}
