-- AlterTable
ALTER TABLE "operator_contracts" ADD COLUMN     "max_students" INTEGER;

-- CreateTable
CREATE TABLE "operator_contract_budget_assignments" (
    "ocba_id" SERIAL NOT NULL,
    "contract_id" INTEGER NOT NULL,
    "budget_code" VARCHAR(20) NOT NULL,
    "valid_from" DATE NOT NULL,
    "valid_to" DATE,
    "created_by" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_by" INTEGER,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "operator_contract_budget_assignments_pkey" PRIMARY KEY ("ocba_id")
);

-- CreateTable
CREATE TABLE "budget_code_rates" (
    "rate_id" SERIAL NOT NULL,
    "code" VARCHAR(20) NOT NULL,
    "valid_from" DATE NOT NULL,
    "valid_to" DATE,
    "rate" DECIMAL(10,2) NOT NULL,
    "reason" TEXT,
    "created_by" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_by" INTEGER,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "budget_code_rates_pkey" PRIMARY KEY ("rate_id")
);

-- CreateTable
CREATE TABLE "incident_types" (
    "incident_type_id" SERIAL NOT NULL,
    "code" VARCHAR(20) NOT NULL,
    "name" VARCHAR(50) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "incident_types_pkey" PRIMARY KEY ("incident_type_id")
);

-- CreateTable
CREATE TABLE "contract_incidents" (
    "incident_id" SERIAL NOT NULL,
    "contract_id" INTEGER NOT NULL,
    "incident_type_id" INTEGER NOT NULL,
    "severity" VARCHAR(20) NOT NULL,
    "title" VARCHAR(100) NOT NULL,
    "description" TEXT NOT NULL,
    "reported_by" INTEGER NOT NULL,
    "occurred_at" TIMESTAMP(3) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "contract_incidents_pkey" PRIMARY KEY ("incident_id")
);

-- CreateIndex
CREATE UNIQUE INDEX "incident_types_code_key" ON "incident_types"("code");

-- AddForeignKey
ALTER TABLE "operator_contract_budget_assignments" ADD CONSTRAINT "operator_contract_budget_assignments_contract_id_fkey" FOREIGN KEY ("contract_id") REFERENCES "operator_contracts"("contract_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "operator_contract_budget_assignments" ADD CONSTRAINT "operator_contract_budget_assignments_budget_code_fkey" FOREIGN KEY ("budget_code") REFERENCES "budget_codes"("code") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "operator_contract_budget_assignments" ADD CONSTRAINT "operator_contract_budget_assignments_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "staff"("staff_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "operator_contract_budget_assignments" ADD CONSTRAINT "operator_contract_budget_assignments_updated_by_fkey" FOREIGN KEY ("updated_by") REFERENCES "staff"("staff_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "budget_code_rates" ADD CONSTRAINT "budget_code_rates_code_fkey" FOREIGN KEY ("code") REFERENCES "budget_codes"("code") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "budget_code_rates" ADD CONSTRAINT "budget_code_rates_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "staff"("staff_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "budget_code_rates" ADD CONSTRAINT "budget_code_rates_updated_by_fkey" FOREIGN KEY ("updated_by") REFERENCES "staff"("staff_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "contract_incidents" ADD CONSTRAINT "contract_incidents_contract_id_fkey" FOREIGN KEY ("contract_id") REFERENCES "operator_contracts"("contract_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "contract_incidents" ADD CONSTRAINT "contract_incidents_incident_type_id_fkey" FOREIGN KEY ("incident_type_id") REFERENCES "incident_types"("incident_type_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "contract_incidents" ADD CONSTRAINT "contract_incidents_reported_by_fkey" FOREIGN KEY ("reported_by") REFERENCES "staff"("staff_id") ON DELETE RESTRICT ON UPDATE CASCADE;
