const { PrismaClient } = require('../generated/prisma/client');
const prisma = new PrismaClient();

async function createTermPeriod() {
  console.log('Creating term period...');

  try {
    // Check if term period already exists
    const existingTerm = await prisma.establishmentTermPeriod.findFirst();
    if (existingTerm) {
      console.log('Term period already exists');
      return;
    }

    // Get first establishment
    const establishment = await prisma.establishment.findFirst();
    if (!establishment) {
      console.log('No establishment found');
      return;
    }

    // Create a simple term period
    const termPeriod = await prisma.establishmentTermPeriod.create({
      data: {
        estabId: establishment.id,
        name: 'Autumn Term 2024',
        validFrom: new Date('2024-09-01'),
        validTo: new Date('2024-12-20')
      }
    });

    console.log(`Created term period: ${termPeriod.name}`);
  } catch (error) {
    console.error('Error creating term period:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

createTermPeriod()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });
