import { useState } from 'react';
import { FaEdit, FaHistory, FaSave } from 'react-icons/fa';
import { getCookie } from '../../../utils/helperFunctions';

export default function BudgetCodeAssignment({ contractId, budgetCodes, assignment, history, onUpdate }) {
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({
    budgetCode: '',
    validFrom: new Date().toISOString().slice(0, 10),
    validTo: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleEditClick = () => {
    setEditForm({
      budgetCode: assignment?.budgetCode || '',
      validFrom: assignment?.validFrom ? new Date(assignment.validFrom).toISOString().slice(0, 10) : new Date().toISOString().slice(0, 10),
      validTo: assignment?.validTo ? new Date(assignment.validTo).toISOString().slice(0, 10) : ''
    });
    setIsEditing(true);
    setError('');
  };

  const handleSave = async () => {
    if (!editForm.budgetCode || !editForm.validFrom) {
      setError('Budget Code and Valid From date are required');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const token = getCookie('TMS_clientToken');
      const response = await fetch(
        `${import.meta.env.VITE_BACKEND_URL}/finances/contracts/${contractId}/budget-assignment`,
        {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(editForm)
        }
      );

      const data = await response.json();
      
      if (response.ok && data.success) {
        setIsEditing(false);
        onUpdate();
      } else {
        setError(data.message || 'Failed to update budget assignment');
      }
    } catch (error) {
      console.error('Error updating budget assignment:', error);
      setError('Network error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setError('');
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-GB');
  };

  const getCurrentRate = () => {
    if (!assignment?.budget?.rates?.[0]) return null;
    return assignment.budget.rates[0].rate;
  };

  const getSelectedBudgetCode = () => {
    return budgetCodes.find(code => code.code === editForm.budgetCode);
  };

  return (
    <div className="space-y-6">
      {/* Current Assignment */}
      <div className="bg-white rounded-md shadow-sm overflow-hidden">
        <div className="px-4 py-3 bg-white border-b border-black/[0.1] flex items-center justify-between">
          <h3 className="font-medium text-gray-700">Budget Code Assignment</h3>
          {!isEditing && (
            <button
              onClick={handleEditClick}
              className="flex items-center px-3 py-1 bg-purple-600 text-white rounded text-sm hover:bg-purple-700"
            >
              <FaEdit className="mr-1" />
              Edit
            </button>
          )}
        </div>

        <div className="p-4">
          {error && (
            <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded text-sm">
              {error}
            </div>
          )}

          {isEditing ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Budget Code *
                  </label>
                  <select
                    value={editForm.budgetCode}
                    onChange={(e) => setEditForm(prev => ({ ...prev, budgetCode: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  >
                    <option value="">Select budget code</option>
                    {budgetCodes.map((code) => (
                      <option key={code.code} value={code.code}>
                        {code.code} - {code.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Valid From *
                  </label>
                  <input
                    type="date"
                    value={editForm.validFrom}
                    onChange={(e) => setEditForm(prev => ({ ...prev, validFrom: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Valid To
                  </label>
                  <input
                    type="date"
                    value={editForm.validTo}
                    onChange={(e) => setEditForm(prev => ({ ...prev, validTo: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Daily Rate
                  </label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg text-sm text-gray-900">
                    {editForm.budgetCode && getSelectedBudgetCode() ?
                      formatCurrency(getSelectedBudgetCode().currentRate || 0) :
                      '£0.00'
                    }
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={handleCancel}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSave}
                  disabled={isLoading}
                  className="px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-lg hover:bg-purple-700 disabled:opacity-50"
                >
                  {isLoading ? (
                    <>
                      <span className="animate-spin inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></span>
                      Saving...
                    </>
                  ) : (
                    <>
                      <FaSave className="mr-2" />
                      Save Changes
                    </>
                  )}
                </button>
              </div>
            </div>
          ) : assignment ? (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm text-gray-500 mb-1">Budget Code</label>
                <p className="font-medium text-gray-900">{assignment.budgetCode}</p>
                <p className="text-sm text-gray-600">{assignment.budget?.name}</p>
              </div>
              <div>
                <label className="block text-sm text-gray-500 mb-1">Valid From</label>
                <p className="font-medium text-gray-900">{formatDate(assignment.validFrom)}</p>
              </div>
              <div>
                <label className="block text-sm text-gray-500 mb-1">Valid To</label>
                <p className="font-medium text-gray-900">
                  {assignment.validTo ? formatDate(assignment.validTo) : 'Open-ended'}
                </p>
              </div>
              <div>
                <label className="block text-sm text-gray-500 mb-1">Daily Rate</label>
                <p className="font-medium text-gray-900">
                  {getCurrentRate() ? formatCurrency(getCurrentRate()) : '-'}
                </p>
              </div>
            </div>
          ) : (
            <div className="text-center py-6">
              <div className="text-gray-400 text-3xl mb-2">💰</div>
              <p className="text-gray-500 text-sm mb-4">No budget code assigned to this contract</p>
              <button
                onClick={handleEditClick}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 text-sm"
              >
                Assign Budget Code
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Assignment History */}
      <div className="bg-white rounded-md shadow-sm overflow-hidden">
        <div className="px-4 py-3 bg-white border-b border-black/[0.1] flex items-center">
          <FaHistory className="mr-2 text-gray-500" />
          <h3 className="font-medium text-gray-700">Budget Code Assignment History</h3>
        </div>

        <div className="p-4">
          {history.length === 0 ? (
            <div className="text-center py-6">
              <div className="text-gray-400 text-3xl mb-2">📋</div>
              <p className="text-gray-500 text-sm">No assignment history</p>
            </div>
          ) : (
            <div className="space-y-3">
              {history.map((entry) => (
                <div key={entry.ocbaId} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">
                      {entry.budgetCode} - {entry.budget?.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatDate(entry.validFrom)} - {entry.validTo ? formatDate(entry.validTo) : 'Current'}
                    </p>
                    <div className="flex items-center text-xs text-gray-500 mt-1">
                      <span>By {entry.createdByStaff?.firstName} {entry.createdByStaff?.lastName}</span>
                      <span className="mx-2">•</span>
                      <span>{formatDate(entry.createdAt)}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
