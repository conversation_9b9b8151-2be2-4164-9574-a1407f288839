import { useEffect, useState } from 'react';
import { FiArrowLeft, FiSave } from 'react-icons/fi';
import { useNavigate } from 'react-router-dom';
import { getCookie } from '../../../utils/helperFunctions';

export default function AddNewContract() {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    operatorId: '',
    code: '',
    name: '',
    maxStudents: '',
    validFrom: '',
    validTo: '',
    budgetCode: ''
  });
  const [operators, setOperators] = useState([]);
  const [budgetCodes, setBudgetCodes] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingOperators, setIsLoadingOperators] = useState(false);
  const [isLoadingBudgetCodes, setIsLoadingBudgetCodes] = useState(false);
  const [error, setError] = useState('');

  // Fetch operators and budget codes on component mount
  useEffect(() => {
    fetchOperators();
    fetchBudgetCodes();
  }, []);

  const fetchOperators = async () => {
    setIsLoadingOperators(true);
    try {
      const token = getCookie('TMS_clientToken');
      // Fetch all operators without search filter
      const response = await fetch(`${import.meta.env.VITE_BACKEND_URL}/contracts/operators?search=`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();
      if (response.ok) {
        setOperators(data.data || []);
        console.log('Loaded operators:', data.data);
      } else {
        console.error('Failed to fetch operators:', data.message);
      }
    } catch (error) {
      console.error('Error fetching operators:', error);
    } finally {
      setIsLoadingOperators(false);
    }
  };

  const fetchBudgetCodes = async () => {
    setIsLoadingBudgetCodes(true);
    try {
      const token = getCookie('TMS_clientToken');
      const response = await fetch(`${import.meta.env.VITE_BACKEND_URL}/contracts/budget-codes`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();
      if (response.ok) {
        setBudgetCodes(data.data || []);
        console.log('Loaded budget codes:', data.data);
      } else {
        console.error('Failed to fetch budget codes:', data.message);
        setError('Failed to load budget codes. Please refresh the page.');
      }
    } catch (error) {
      console.error('Error fetching budget codes:', error);
    } finally {
      setIsLoadingBudgetCodes(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');



    try {
      const token = getCookie('TMS_clientToken');
      const response = await fetch(`${import.meta.env.VITE_BACKEND_URL}/contracts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to create contract');
      }

      console.log('Contract created successfully:', data.data);

      // Show success message
      const successMessage = `Contract "${formData.name}" (${formData.code}) created successfully with budget code ${formData.budgetCode}!`;
      alert(successMessage);

      navigate('/contracts');
    } catch (err) {
      setError(err.message || 'Failed to create contract. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <div className="flex-1 bg-gray-50 p-6">
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <button
            onClick={() => navigate('/contracts')}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <FiArrowLeft className="w-5 h-5" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Add New Contract</h1>
            <p className="text-gray-600">Create a new transport operator contract</p>
          </div>
        </div>

        {/* Form */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Operator Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Transport Operator *
              </label>
              <select
                name="operatorId"
                value={formData.operatorId}
                onChange={handleChange}
                required
                disabled={isLoadingOperators}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              >
                <option value="">
                  {isLoadingOperators ? 'Loading operators...' : 'Select a transport operator'}
                </option>
                {operators.map(operator => (
                  <option key={operator.id} value={operator.id}>
                    {operator.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Contract Code *
                </label>
                <input
                  type="text"
                  name="code"
                  value={formData.code}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="e.g., BTS-001"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Contract Name *
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="e.g., Varndean AM Run"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Max Students
                </label>
                <input
                  type="number"
                  name="maxStudents"
                  value={formData.maxStudents}
                  onChange={handleChange}
                  min="1"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="e.g., 16"
                />
              </div>

              {/* Budget Code Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Budget Code *
                </label>
                <select
                  name="budgetCode"
                  value={formData.budgetCode}
                  onChange={handleChange}
                  required
                  disabled={isLoadingBudgetCodes}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  <option value="">
                    {isLoadingBudgetCodes ? 'Loading budget codes...' : 'Select a budget code'}
                  </option>
                  {budgetCodes.map(budgetCode => (
                    <option key={budgetCode.code} value={budgetCode.code}>
                      {budgetCode.code} - {budgetCode.description}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Valid From *
                </label>
                <input
                  type="date"
                  name="validFrom"
                  value={formData.validFrom}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Valid To
                </label>
                <input
                  type="date"
                  name="validTo"
                  value={formData.validTo}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>


            </div>

            {/* Submit Button */}
            <div className="flex justify-end gap-3 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={() => navigate('/contracts')}
                className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50 flex items-center gap-2"
              >
                <FiSave className="w-4 h-4" />
                {isLoading ? 'Creating...' : 'Create Contract'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
