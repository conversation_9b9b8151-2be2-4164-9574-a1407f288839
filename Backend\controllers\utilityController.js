const { PrismaClient } = require('../generated/prisma/client');
const prisma = new PrismaClient();

const getClientTypes = async (req, res) => {
  try {
    const clientTypes = await prisma.clientType.findMany({
      select: {
        id: true,
        code: true,
        name: true,
        description: true
      },
      orderBy: {
        name: 'asc'
      }
    });
    
    res.status(200).json({
      success: true,
      data: clientTypes
    });
  } catch (error) {
    console.error('Error fetching client types:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch client types'
    });
  }
};

const getMedicalNeeds = async (req, res) => {
  try {
    const medicalNeeds = await prisma.medicalNeed.findMany({
      select: {
        id: true,
        code: true,
        description: true
      },
      orderBy: {
        description: 'asc'
      }
    });
    
    res.status(200).json({
      success: true,
      data: medicalNeeds
    });
  } catch (error) {
    console.error('Error fetching medical needs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch medical needs'
    });
  }
};

const getEstablishments = async (req, res) => {
  try {
    const establishments = await prisma.establishment.findMany({
      select: {
        id: true,
        name: true,
        schoolType: true,
        localAuthority: true,
        city: true
      },
      orderBy: {
        name: 'asc'
      }
    });
    
    res.status(200).json({
      success: true,
      data: establishments
    });
  } catch (error) {
    console.error('Error fetching establishments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch establishments'
    });
  }
};

// Get communication types
const getCommunicationTypes = async (req, res) => {
  try {
    // Since communication types appear to be predefined in the application
    // rather than stored in the database (based on the React component),
    // we'll return a predefined list
    const communicationTypes = [
      { value: 'Email', label: 'Email' },
      { value: 'Phone', label: 'Phone Call' },
      { value: 'Meeting', label: 'Meeting' },
      { value: 'Letter', label: 'Letter' },
      { value: 'SMS', label: 'SMS' }
    ];
    
    res.status(200).json(communicationTypes);
  } catch (error) {
    console.error('Error fetching communication types:', error);
    res.status(500).json({ message: 'Failed to fetch communication types' });
  }
};

// Get communication groups
const getCommunicationGroups = async (req, res) => {
  try {
    // Since communication groups also appear to be predefined
    const communicationGroups = [
      { value: 'General', label: 'General' },
      { value: 'Transport', label: 'Transport' },
      { value: 'Medical', label: 'Medical' },
      { value: 'Behavior', label: 'Behavior' },
      { value: 'Attendance', label: 'Attendance' }
    ];
    
    res.status(200).json(communicationGroups);
  } catch (error) {
    console.error('Error fetching communication groups:', error);
    res.status(500).json({ message: 'Failed to fetch communication groups' });
  }
};

// Get note types
const getNotesTypes = async (req, res) => {
  try {
    // From the React component, we can see these are the note types used
    const noteTypes = [
      { value: 'General', label: 'General' },
      { value: 'Urgent', label: 'Urgent' },
      { value: 'Phone Call', label: 'Phone Call' },
      { value: 'Meeting', label: 'Meeting' },
      { value: 'Email', label: 'Email' }
    ];
    
    res.status(200).json(noteTypes);
  } catch (error) {
    console.error('Error fetching note types:', error);
    res.status(500).json({ message: 'Failed to fetch note types' });
  }
};

// Get year groups
const getYearGroups = async (req, res) => {
  try {
    const yearGroups = await prisma.yearGroup.findMany({
      select: {
        id: true,
        code: true,
        name: true
      },
      orderBy: {
        id: 'asc'
      }
    });
    
    res.status(200).json({
      success: true,
      data: yearGroups
    });
  } catch (error) {
    console.error('Error fetching year groups:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch year groups'
    });
  }
};

// Get last update
const getLastUpdate = async (req, res) => {
  try {
    const { studentId } = req.params;
    const {entity} = req.query;
    
    if (!studentId) {
      return res.status(400).json({
        success: false,
        message: 'Student ID is required'
      });
    }

    // Get the most recent audit log for the student
    const lastUpdate = await prisma.auditLog.findFirst({
      where: {
        entity,
        entityId: parseInt(studentId)
      },
      orderBy: {
        changedAt: 'desc'
      },
      include: {
        staff: true
      }
    });

    if (!lastUpdate) {
      return res.status(200).json({
        success: true,
        data: null,
        message: 'No updates found for this student'
      });
    }

    // Transform audit log data to match expected format
    const transformedData = {
      targetName: lastUpdate.newData?.targetName || "Unknown",
      isPrimaryTarget: lastUpdate.newData?.isPrimaryTarget || false,
      message: lastUpdate.newData?.message || null,
      date: lastUpdate.changedAt,
      authorName: `${lastUpdate.staff.firstName} ${lastUpdate.staff.lastName}`
    };

    return res.status(200).json({
      success: true,
      data: transformedData
    });
  } catch (error) {
    console.error('Error fetching last update:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch last update',
      error: error.message
    });
  }
};

// Add last update
const addLastUpdate = async (req, res) => {
  try {
    const { targetName, message, isPrimaryTarget, studentId, entity, operation } = req.body;

    const author = await prisma.staff.findUnique({
      where: { id: req.staff.id }
    });
    const authorName = author.firstName + ' ' + author.lastName;

    // Validate required fields
    if (!authorName || !targetName || !studentId) {
      return res.status(400).json({
        success: false,
        message: 'Author name, target name, and student ID are required'
      });
    }

    // Get all previous updates for this entity and entityId to build combined oldData
    const previousUpdates = await prisma.auditLog.findMany({
      where: {
        entity,
        entityId: parseInt(studentId)
      },
      orderBy: {
        changedAt: 'desc'
      }
    });

    // Build combined oldData from all previous updates
    let combinedOldData = {};
    
    // Iterate through previous updates in reverse chronological order
    for (const update of previousUpdates.reverse()) {
      if (update.oldData) {
        combinedOldData = { ...combinedOldData, ...update.oldData };
      }
      if (update.newData) {
        combinedOldData = { ...combinedOldData, ...update.newData };
      }
    }

    // Create new audit log record
    const newUpdate = await prisma.auditLog.create({
      data: {
        entity,
        entityId: parseInt(studentId),
        operation,
        changedBy: req.staff.id,
        changedAt: new Date(),
        oldData: Object.keys(combinedOldData).length > 0 ? combinedOldData : null,
        newData: {
          targetName,
          message: message || null,
          isPrimaryTarget: isPrimaryTarget || false
        }
      }
    });

    // Transform response to match expected format
    const transformedResponse = {
      targetName,
      message: message || null,
      isPrimaryTarget: isPrimaryTarget || false,
      date: newUpdate.changedAt,
      authorName
    };

    return res.status(200).json({
      success: true,
      data: transformedResponse,
      message: 'Last update added successfully'
    });
  } catch (error) {
    console.error('Error adding last update:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to add last update',
      error: error.message
    });
  }
};

module.exports = {
  getClientTypes,
  getMedicalNeeds,
  getEstablishments,
  getCommunicationTypes,
  getCommunicationGroups,
  getNotesTypes,
  getYearGroups,
  getLastUpdate,
  addLastUpdate,
};