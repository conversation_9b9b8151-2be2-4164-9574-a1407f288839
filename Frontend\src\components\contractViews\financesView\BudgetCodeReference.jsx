import { useState } from 'react';
import { FaEdit, FaHistory, FaSave, FaTimes } from 'react-icons/fa';
import { getCookie } from '../../../utils/helperFunctions';

export default function BudgetCodeReference({ budgetCodes, budgetHistory, onUpdate }) {
  const [isEditing, setIsEditing] = useState(false);
  const [editingCode, setEditingCode] = useState(null);
  const [editForm, setEditForm] = useState({
    rate: '',
    reason: '',
    validFrom: new Date().toISOString().slice(0, 10)
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleEditClick = (code) => {
    setEditingCode(code);
    setEditForm({
      rate: code.currentRate || '',
      reason: '',
      validFrom: new Date().toISOString().slice(0, 10)
    });
    setIsEditing(true);
    setError('');
  };

  const handleSave = async () => {
    if (!editForm.rate || !editForm.validFrom) {
      setError('Rate and Valid From date are required');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const token = getCookie('TMS_clientToken');
      const response = await fetch(
        `${import.meta.env.VITE_BACKEND_URL}/finances/budget-codes/${editingCode.code}/rate`,
        {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(editForm)
        }
      );

      const data = await response.json();
      
      if (response.ok && data.success) {
        setIsEditing(false);
        setEditingCode(null);
        onUpdate();
      } else {
        setError(data.message || 'Failed to update budget code rate');
      }
    } catch (error) {
      console.error('Error updating budget code rate:', error);
      setError('Network error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditingCode(null);
    setError('');
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-GB');
  };

  return (
    <div className="space-y-6">
      {editingCode ? (
        /* Edit Side Panel */
        <div className="flex gap-6">
          {/* Edit Form - Right Side */}
          <div className="w-1/2 bg-white rounded-md shadow-sm overflow-hidden">
            <div className="px-4 py-3 bg-white border-b border-black/[0.1] flex items-center justify-between">
              <h3 className="font-medium text-gray-700">
                Edit Rate for {editingCode.code}
              </h3>
              <button
                onClick={handleCancel}
                className="text-gray-400 hover:text-gray-600"
              >
                <FaTimes />
              </button>
            </div>

            <div className="p-6">
              {error && (
                <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded text-sm">
                  {error}
                </div>
              )}

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Daily Rate (£) *
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    value={editForm.rate}
                    onChange={(e) => setEditForm(prev => ({ ...prev, rate: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    placeholder="0.00"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Valid From *
                  </label>
                  <input
                    type="date"
                    value={editForm.validFrom}
                    onChange={(e) => setEditForm(prev => ({ ...prev, validFrom: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Reason for Change
                  </label>
                  <textarea
                    value={editForm.reason}
                    onChange={(e) => setEditForm(prev => ({ ...prev, reason: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    rows="3"
                    placeholder="Optional reason for this rate change..."
                  />
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    onClick={handleCancel}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleSave}
                    disabled={isLoading}
                    className="px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-lg hover:bg-purple-700 disabled:opacity-50"
                  >
                    {isLoading ? (
                      <>
                        <span className="animate-spin inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></span>
                        Saving...
                      </>
                    ) : (
                      <>
                        <FaSave className="mr-2" />
                        Save Changes
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        /* Budget Code Reference Table */
        <div className="bg-white rounded-md shadow-sm overflow-hidden">
        <div className="px-4 py-3 bg-white border-b border-black/[0.1] flex items-center justify-between">
          <h3 className="font-medium text-gray-700">Budget Code Reference</h3>
          {!isEditing && (
            <button
              onClick={() => setIsEditing(true)}
              className="flex items-center px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600"
            >
              <FaEdit className="mr-1" />
              Edit Reference Table
            </button>
          )}
        </div>

        {/* Budget Code Grid - Matching the image layout */}
        <div className="p-6">
          <div className="grid grid-cols-3 gap-8">
            {budgetCodes.slice(0, 3).map((code) => (
              <div key={code.code} className="text-center">
                <div className="text-sm font-medium text-gray-600 mb-1">
                  {code.code}
                </div>
                <div className="text-lg font-semibold text-gray-900 mb-2">
                  {code.currentRate ? formatCurrency(code.currentRate) : '£0.00'}
                </div>
                {isEditing && (
                  <button
                    onClick={() => handleEditClick(code)}
                    className="text-purple-600 hover:text-purple-900 text-sm"
                  >
                    <FaEdit className="inline mr-1" />
                    Edit
                  </button>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Reference History Section */}
        <div className="border-t border-gray-200">
          <div className="px-4 py-3 bg-gray-50 border-b border-gray-200">
            <h4 className="text-sm font-medium text-gray-700 flex items-center">
              <FaHistory className="mr-2" />
              Reference History
            </h4>
          </div>
          <div className="p-4">
            {budgetHistory.length === 0 ? (
              <div className="text-center py-4">
                <p className="text-gray-500 text-sm">No rate changes recorded</p>
              </div>
            ) : (
              <div className="space-y-3">
                {budgetHistory.slice(0, 5).map((entry) => (
                  <div key={entry.rateId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">
                        {entry.code}: {formatCurrency(entry.rate)}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatDate(entry.validFrom)} - {entry.validTo ? formatDate(entry.validTo) : 'Current'}
                      </p>
                      {entry.reason && (
                        <p className="text-xs text-gray-600 mt-1">{entry.reason}</p>
                      )}
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-gray-500">
                        By {entry.createdByStaff?.firstName} {entry.createdByStaff?.lastName}
                      </p>
                      <p className="text-xs text-gray-500">
                        Updated {formatDate(entry.createdAt)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {isEditing && (
          <div className="px-4 py-3 bg-gray-50 border-t border-gray-200 flex justify-end space-x-2">
            <button
              onClick={handleCancel}
              className="flex items-center px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600"
            >
              <FaTimes className="mr-1" />
              Cancel
            </button>
          </div>
        )}
      </div>
      )}

    </div>
  );
}
