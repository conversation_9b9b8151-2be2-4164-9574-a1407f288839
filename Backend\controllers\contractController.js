// /controllers/contractController.js
const { PrismaClient } = require('../generated/prisma/client');
const prisma = new PrismaClient();

// Get transport operators with search functionality
const getOperators = async (req, res) => {
  try {
    const { search } = req.query;

    let whereClause = {};
    if (search && search.trim().length >= 2) {
      whereClause = {
        name: {
          contains: search.trim(),
          mode: 'insensitive'
        }
      };
    }

    const operators = await prisma.transportOperator.findMany({
      where: whereClause,
      include: {
        operatorContracts: {
          include: {
            _count: {
              select: {
                travelAssignments: true
              }
            }
          }
        }
      },
      orderBy: {
        name: 'asc'
      },
      take: 10
    });

    const formattedOperators = operators.map(operator => ({
      id: operator.id,
      name: operator.name,
      phone: operator.phone,
      email: operator.email,
      contractCount: operator.operatorContracts.length
    }));

    res.json({
      success: true,
      data: formattedOperators
    });
  } catch (error) {
    console.error('Error fetching operators:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch operators'
    });
  }
};

// Get contracts for a specific operator
const getOperatorContracts = async (req, res) => {
  try {
    const { operatorId } = req.params;

    const contracts = await prisma.operatorContract.findMany({
      where: {
        operatorId: parseInt(operatorId)
      },
      include: {
        operator: true,
        travelAssignments: {
          select: {
            studentId: true
          }
        }
      },
      orderBy: {
        code: 'asc'
      }
    });

    const formattedContracts = contracts.map(contract => {
      // Count unique students (not assignments)
      const uniqueStudentIds = new Set(contract.travelAssignments.map(assignment => assignment.studentId));
      const currentStudents = uniqueStudentIds.size;

      return {
        id: contract.id,
        code: contract.code,
        name: contract.name,
        maxStudents: contract.maxStudents,
        currentStudents: currentStudents,
        validFrom: contract.validFrom,
        validTo: contract.validTo,
        isActive: !contract.validTo || new Date(contract.validTo) > new Date(),
        operator: {
          id: contract.operator.id,
          name: contract.operator.name
        }
      };
    });

    res.json({
      success: true,
      data: formattedContracts
    });
  } catch (error) {
    console.error('Error fetching operator contracts:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch operator contracts'
    });
  }
};

// Get contract details
const getContractDetails = async (req, res) => {
  try {
    const { contractId } = req.params;
    console.log('getContractDetails called with contractId:', contractId, 'params:', req.params);

    const contract = await prisma.operatorContract.findUnique({
      where: {
        id: parseInt(contractId)
      },
      include: {
        operator: true,
        travelAssignments: {
          select: {
            studentId: true
          }
        }
      }
    });

    if (!contract) {
      return res.status(404).json({
        success: false,
        message: 'Contract not found'
      });
    }

    // Count unique students (not assignments)
    const uniqueStudentIds = new Set(contract.travelAssignments.map(assignment => assignment.studentId));
    const currentStudents = uniqueStudentIds.size;

    const formattedContract = {
      id: contract.id,
      code: contract.code,
      name: contract.name,
      maxStudents: contract.maxStudents,
      currentStudents: currentStudents,
      validFrom: contract.validFrom,
      validTo: contract.validTo,
      isActive: !contract.validTo || new Date(contract.validTo) > new Date(),
      createdAt: contract.createdAt,
      updatedAt: contract.updatedAt,
      operator: {
        id: contract.operator.id,
        name: contract.operator.name,
        phone: contract.operator.phone,
        email: contract.operator.email
      }
    };

    res.json({
      success: true,
      data: formattedContract
    });
  } catch (error) {
    console.error('Error fetching contract details:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch contract details'
    });
  }
};

// Update contract details
const updateContract = async (req, res) => {
  try {
    const { contractId } = req.params;
    const { code, name, maxStudents, validFrom, validTo, status } = req.body;

    // Prepare update data
    const updateData = {
      name,
      maxStudents: maxStudents ? parseInt(maxStudents) : null,
      validFrom: validFrom ? new Date(validFrom) : undefined,
      validTo: validTo ? new Date(validTo) : null
    };

    // Include code if provided
    if (code) {
      updateData.code = code;
    }

    // Handle status - if status is 'Inactive', set validTo to current date
    if (status === 'Inactive') {
      updateData.validTo = new Date();
    } else if (status === 'Active' && !validTo) {
      updateData.validTo = null; // Remove validTo to make it active
    }

    const updatedContract = await prisma.operatorContract.update({
      where: {
        id: parseInt(contractId)
      },
      data: updateData,
      include: {
        operator: true,
        travelAssignments: {
          select: {
            studentId: true
          }
        }
      }
    });

    // Count unique students (not assignments)
    const uniqueStudentIds = new Set(updatedContract.travelAssignments.map(assignment => assignment.studentId));
    const currentStudents = uniqueStudentIds.size;

    const formattedContract = {
      id: updatedContract.id,
      code: updatedContract.code,
      name: updatedContract.name,
      maxStudents: updatedContract.maxStudents,
      currentStudents: currentStudents,
      validFrom: updatedContract.validFrom,
      validTo: updatedContract.validTo,
      isActive: !updatedContract.validTo || new Date(updatedContract.validTo) > new Date(),
      operator: {
        id: updatedContract.operator.id,
        name: updatedContract.operator.name
      }
    };

    res.json({
      success: true,
      data: formattedContract,
      message: 'Contract updated successfully'
    });
  } catch (error) {
    console.error('Error updating contract:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update contract'
    });
  }
};

// Create new transport operator
const createOperator = async (req, res) => {
  try {
    const { name, phone, email } = req.body;

    // Validate required fields
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Operator name is required'
      });
    }

    const newOperator = await prisma.transportOperator.create({
      data: {
        name,
        phone: phone || null,
        email: email || null
      }
    });

    res.status(201).json({
      success: true,
      data: {
        id: newOperator.id,
        name: newOperator.name,
        phone: newOperator.phone,
        email: newOperator.email
      },
      message: 'Transport operator created successfully'
    });
  } catch (error) {
    console.error('Error creating operator:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create transport operator'
    });
  }
};

// Create new contract
const createContract = async (req, res) => {
  try {
    const {
      operatorId,
      code,
      name,
      maxStudents,
      validFrom,
      validTo,
      budgetCode,
      assignmentValidFrom,
      assignmentValidTo
    } = req.body;

    // Validate required fields
    if (!operatorId || !code || !name || !validFrom || !budgetCode || !assignmentValidFrom) {
      return res.status(400).json({
        success: false,
        message: 'Operator ID, contract code, name, valid from date, budget code, and assignment valid from date are required'
      });
    }

    // Check if operator exists
    const operator = await prisma.transportOperator.findUnique({
      where: { id: parseInt(operatorId) }
    });

    if (!operator) {
      return res.status(404).json({
        success: false,
        message: 'Transport operator not found'
      });
    }

    // Check if budget code exists
    const budgetCodeExists = await prisma.budgetCode.findUnique({
      where: { code: budgetCode }
    });

    if (!budgetCodeExists) {
      return res.status(404).json({
        success: false,
        message: 'Budget code not found'
      });
    }

    // Check if contract code already exists
    const existingContract = await prisma.operatorContract.findUnique({
      where: { code }
    });

    if (existingContract) {
      return res.status(400).json({
        success: false,
        message: 'Contract code already exists'
      });
    }

    const staffId = req.user?.id || 1; // Default to admin for now

    // Create contract and budget assignment in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the contract
      const newContract = await tx.operatorContract.create({
        data: {
          operatorId: parseInt(operatorId),
          code,
          name,
          maxStudents: maxStudents ? parseInt(maxStudents) : null,
          validFrom: new Date(validFrom),
          validTo: validTo ? new Date(validTo) : null
        },
        include: {
          operator: true,
          travelAssignments: {
            select: {
              studentId: true
            }
          }
        }
      });

      // Create the budget assignment
      await tx.operatorContractBudgetAssignment.create({
        data: {
          contractId: newContract.id,
          budgetCode: budgetCode,
          validFrom: new Date(assignmentValidFrom),
          validTo: assignmentValidTo ? new Date(assignmentValidTo) : null,
          createdBy: staffId
        }
      });

      return newContract;
    });

    // Count unique students (not assignments)
    const uniqueStudentIds = new Set(result.travelAssignments.map(assignment => assignment.studentId));
    const currentStudents = uniqueStudentIds.size;

    const formattedContract = {
      id: result.id,
      code: result.code,
      name: result.name,
      maxStudents: result.maxStudents,
      currentStudents: currentStudents,
      validFrom: result.validFrom,
      validTo: result.validTo,
      isActive: !result.validTo || new Date(result.validTo) > new Date(),
      operator: {
        id: result.operator.id,
        name: result.operator.name
      }
    };

    res.status(201).json({
      success: true,
      data: formattedContract,
      message: 'Contract created successfully'
    });
  } catch (error) {
    console.error('Error creating contract:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create contract'
    });
  }
};

module.exports = {
  getOperators,
  getOperatorContracts,
  getContractDetails,
  updateContract,
  createOperator,
  createContract
};
