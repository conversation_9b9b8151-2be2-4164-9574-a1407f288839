import { useEffect, useState } from 'react';
import { FiPlus, FiSearch } from 'react-icons/fi';
import { useNavigate } from 'react-router-dom';
import { getCookie } from '../../utils/helperFunctions';

function ContractsSidebar({
  selectedOperator,
  setSelectedOperator,
  selectedContract,
  setSelectedContract,
  currentView,
  onAddNewButtonClicked
}) {
  const [searchText, setSearchText] = useState('');
  const [operators, setOperators] = useState([]);
  const [contracts, setContracts] = useState([]);
  const [filteredOperators, setFilteredOperators] = useState([]);
  const [showDropdown, setShowDropdown] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  // Search for operators
  useEffect(() => {
    const searchOperators = async () => {
      if (searchText.length < 2) {
        setFilteredOperators([]);
        setShowDropdown(false);
        return;
      }

      setIsLoading(true);
      setError('');

      try {
        const token = getCookie('TMS_clientToken');
        const response = await fetch(
          `${import.meta.env.VITE_BACKEND_URL}/contracts/operators?search=${encodeURIComponent(searchText)}`,
          {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }
        );

        const data = await response.json();

        if (response.ok && data.success) {
          setFilteredOperators(data.data);
          setShowDropdown(true);
        } else {
          setError(data.message || 'Failed to search operators');
          setFilteredOperators([]);
          setShowDropdown(false);
        }
      } catch (error) {
        console.error('Error searching operators:', error);
        setError('Network error occurred');
        setFilteredOperators([]);
        setShowDropdown(false);
      } finally {
        setIsLoading(false);
      }
    };

    const debounceTimer = setTimeout(searchOperators, 300);
    return () => clearTimeout(debounceTimer);
  }, [searchText]);

  // Fetch contracts when operator is selected
  useEffect(() => {
    const fetchContracts = async () => {
      if (!selectedOperator) {
        setContracts([]);
        return;
      }

      setIsLoading(true);
      setError('');

      try {
        const token = getCookie('TMS_clientToken');
        const response = await fetch(
          `${import.meta.env.VITE_BACKEND_URL}/contracts/operators/${selectedOperator.id}/contracts`,
          {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }
        );

        const data = await response.json();

        if (response.ok && data.success) {
          setContracts(data.data);
        } else {
          setError(data.message || 'Failed to fetch contracts');
          setContracts([]);
        }
      } catch (error) {
        console.error('Error fetching contracts:', error);
        setError('Network error occurred');
        setContracts([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchContracts();
  }, [selectedOperator]);

  const handleSearch = (e) => {
    const query = e.target.value;
    setSearchText(query);

    // Show dropdown when user starts typing
    if (query.trim().length >= 1) {
      setShowDropdown(true);
    } else {
      setShowDropdown(false);
    }

    // If user is typing something different from the selected operator, clear the selection
    if (selectedOperator && query !== selectedOperator.name) {
      setSelectedOperator(null);
      setSelectedContract(null);
      setContracts([]);
    }

    // The useEffect will handle the API call
  };

  const handleOperatorSelect = (operator) => {
    if (typeof operator === 'string') {
      const selectedOp = filteredOperators.find(op => op.name === operator);
      if (selectedOp) {
        setSelectedOperator(selectedOp);
        setSearchText(selectedOp.name);
      }
    } else {
      setSelectedOperator(operator);
      setSearchText(operator.name);
    }
    setShowDropdown(false);
    setSelectedContract(null);
  };

  const handleContractSelect = (contract) => {
    setSelectedContract(contract);
  };

  const handleAddNewOperator = () => {
    navigate('/contracts/add-new-operator');
  };

  const handleAddNewContract = () => {
    navigate('/contracts/add-new-contract');
  };



  return (
    <div className="w-64 bg-white border-r border-black/[0.1] flex flex-col h-full relative"
      onClick={() => setShowDropdown(false)} // Hide dropdown when clicking outside
    >
      <div className="p-4 flex-shrink-0">
      <h2 className="font-bold text-xl mb-4">Contracts</h2>

        {/* Add New Buttons */}
        <div className="flex gap-2 mb-4">
          <button
            className="flex items-center gap-1 bg-purple-400 text-white px-3 py-2 rounded-lg text-sm flex-1 justify-center hover:bg-purple-500 cursor-pointer font-medium"
            onClick={handleAddNewContract}
          >
            <FiPlus className="w-4 h-4" />
            <span>New Contract</span>
          </button>

          <button
            className="flex items-center gap-1 bg-purple-700 text-white px-3 py-2 rounded-lg text-sm flex-1 justify-center hover:bg-purple-800 cursor-pointer font-medium"
            onClick={handleAddNewOperator}
          >
            <FiPlus className="w-4 h-4" />
            <span>New Operator</span>
          </button>
        </div>

      <div className="mb-4">
        <h3 className="text-lg font-semibold mb-2">Search & Filter</h3>
        


        <div className="mb-2 relative">
          <label className="font-semibold text-sm text-gray-500">Transport Operator</label>
          <div className="relative">
            <input
              type="text"
              placeholder="Search operators..."
              className="w-full pl-8 pr-8 py-2 border-custom border-custom:focus"
              value={searchText}
              onChange={handleSearch}
              onFocus={() => {
                if (searchText.trim().length >= 1) {
                  setShowDropdown(true);
                }
              }}
            />
            <FiSearch className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
            {searchText && (
              <button
                onClick={() => {
                  setSearchText('');
                  setSelectedOperator(null);
                  setSelectedContract(null);
                  setContracts([]);
                  setFilteredOperators([]);
                  setShowDropdown(false);
                }}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            )}
          </div>

          {/* Dropdown for operators */}
          {showDropdown && (
            <div className="absolute z-10 mt-1 w-full bg-white border rounded shadow max-h-60 overflow-y-auto">
              {isLoading ? (
                <div className="px-3 py-2 text-center text-gray-500">
                  Searching...
                </div>
              ) : filteredOperators.length > 0 ? (
                filteredOperators.map(operator => (
                  <div
                    key={operator.id}
                    className="px-3 py-2 hover:bg-gray-100 cursor-pointer"
                    onClick={() => handleOperatorSelect(operator)}
                  >
                    <div className="font-medium">{operator.name}</div>
                    <div className="text-sm text-gray-500">
                      {operator.contractCount} contract{operator.contractCount !== 1 ? 's' : ''}
                    </div>
                    {operator.phone && (
                      <div className="text-sm text-gray-500">{operator.phone}</div>
                    )}
                  </div>
                ))
              ) : searchText.length >= 2 && (
                <div className="px-3 py-2 text-center text-gray-500">
                  No operators found
                </div>
              )}
            </div>
          )}
        </div>

        <button
          className="bg-purple-700 text-white px-3 py-2 rounded-lg w-full text-sm hover:bg-purple-800 cursor-pointer"
          onClick={() => {
            if (searchText.trim() === '') {
              alert('Please enter an operator name to search.');
              return;
            }
            handleOperatorSelect(searchText);
          }}
        >
          Search Operator
        </button>

        {/* Error display */}
        {error && (
          <div className="mt-2 text-sm text-red-600">
            {error}
          </div>
        )}
      </div>

      </div>

      {/* Operator Contracts Section - Scrollable */}
      {selectedOperator && (
        <div className="flex-1 flex flex-col min-h-0 px-4 pb-4">
          <h3 className="text-lg font-semibold mb-2 flex-shrink-0">Operator Contracts</h3>
          <div className="mb-2 flex-shrink-0">
            <div className="font-medium text-gray-900">{selectedOperator.name}</div>
            <div className="text-sm text-gray-500">{selectedOperator.phone}</div>
          </div>

          {isLoading ? (
            <div className="text-center py-4 text-gray-500 text-sm">
              Loading contracts...
            </div>
          ) : contracts.length > 0 ? (
            <div className="space-y-1 overflow-y-auto flex-1 max-h-96">
              {contracts.map(contract => (
                <div
                  key={contract.id}
                  className={`p-3 border rounded cursor-pointer text-sm ${
                    selectedContract?.id === contract.id
                      ? 'bg-purple-50 border-purple-200'
                      : 'border-gray-200 hover:bg-gray-50'
                  }`}
                  onClick={() => handleContractSelect(contract)}
                >
                  <div className="font-medium text-gray-900 mb-1">{contract.name}</div>
                  <div className="text-xs text-gray-500">
                    Code: {contract.code}
                  </div>
                  <div className="text-xs text-gray-600 mt-1">
                    Occupied: {contract.currentStudents || 0} / {contract.maxStudents || 0} seats
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-4 text-gray-500 text-sm">
              No contracts found
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default ContractsSidebar;
