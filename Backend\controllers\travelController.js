const { PrismaClient } = require('../generated/prisma/client');
const prisma = new PrismaClient();

const getTravelDetails = async (req, res) => {
  try {
    const { studentId } = req.params;
    const studentIdInt = parseInt(studentId);

    if (isNaN(studentIdInt)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid student ID'
      });
    }

    const currentDate = new Date();
    
    // Fetch travel assignments for both AM and PM runs
    const travelAssignments = await prisma.studentTravelAssignment.findMany({
      where: {
        studentId: studentIdInt,
        termPeriod: {
          validFrom: { lte: currentDate },
          validTo: { gte: currentDate }
        }
      },
      include: {
        operatorContract: {
          include: {
            operator: true
          }
        },
        termPeriod: true,
        travelLocations: {
          include: {
            establishment: true,
            contactAddress: {
              include: {
                contact: true
              }
            }
          }
        },
        routeMetrics: {
          orderBy: {
            computedAt: 'desc'
          },
          take: 1
        },
        // ADD: Include travel days to show actual pattern
        travelDays: {
          orderBy: {
            dayOfWeek: 'asc'
          }
        }
      }
    });

    // FIX: Get current budget code with proper date filtering
    const currentCodeAssignment = await prisma.studentCodeAssignment.findFirst({
      where: {
        studentId: studentIdInt,
        validFrom: { lte: currentDate },
        OR: [
          { validTo: null },
          { validTo: { gte: currentDate } }
        ]
      },
      include: {
        budget: true,
        eligibility: true,
        funding: true
      },
      orderBy: {
        validFrom: 'desc'
      }
    });

    // Structure the data for AM and PM runs
    const morningRun = travelAssignments.find(assignment => assignment.runType === 'AM');
    const afternoonRun = travelAssignments.find(assignment => assignment.runType === 'PM');

    const formatTravelRun = (assignment) => {
      if (!assignment) return null;

      const pickupLocation = assignment.travelLocations.find(loc => loc.type === 'pickup');
      const dropoffLocation = assignment.travelLocations.find(loc => loc.type === 'dropoff');

      // Format pickup location
      let formattedPickupLocation = null;
      if (pickupLocation) {
        if (pickupLocation.isSchool && pickupLocation.establishment) {
          formattedPickupLocation = {
            addressLine1: pickupLocation.establishment.addressLine1,
            addressLine2: null,
            city: pickupLocation.establishment.city,
            postcode: pickupLocation.establishment.postcode,
            departAfterTime: pickupLocation.departAfterTime
          };
        } else if (!pickupLocation.isSchool && pickupLocation.contactAddress) {
          formattedPickupLocation = {
            addressLine1: pickupLocation.contactAddress.addressLine1,
            addressLine2: pickupLocation.contactAddress.addressLine2,
            city: pickupLocation.contactAddress.city,
            postcode: pickupLocation.contactAddress.postcode,
            departAfterTime: pickupLocation.departAfterTime
          };
        } else {
          formattedPickupLocation = {
            addressLine1: pickupLocation.addressLine1,
            addressLine2: pickupLocation.addressLine2,
            city: 'N/A',
            postcode: pickupLocation.postcode,
            departAfterTime: pickupLocation.departAfterTime
          };
        }
      }

      // Format dropoff location
      let formattedDropoffLocation = null;
      if (dropoffLocation) {
        if (dropoffLocation.isSchool && dropoffLocation.establishment) {
          formattedDropoffLocation = {
            addressLine1: dropoffLocation.establishment.addressLine1,
            addressLine2: null,
            city: dropoffLocation.establishment.city,
            postcode: dropoffLocation.establishment.postcode,
            arriveBeforeTime: dropoffLocation.arriveBeforeTime
          };
        } else if (!dropoffLocation.isSchool && dropoffLocation.contactAddress) {
          formattedDropoffLocation = {
            addressLine1: dropoffLocation.contactAddress.addressLine1,
            addressLine2: dropoffLocation.contactAddress.addressLine2,
            city: dropoffLocation.contactAddress.city,
            postcode: dropoffLocation.contactAddress.postcode,
            arriveBeforeTime: dropoffLocation.arriveBeforeTime
          };
        } else {
          formattedDropoffLocation = {
            addressLine1: dropoffLocation.addressLine1,
            addressLine2: dropoffLocation.addressLine2,
            city: 'N/A',
            postcode: dropoffLocation.postcode,
            arriveBeforeTime: dropoffLocation.arriveBeforeTime
          };
        }
      }

      // FIX: Generate pattern from travel days
      const dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
      const travelDayNames = assignment.travelDays
        .map(td => dayNames[td.dayOfWeek - 1])
        .filter(Boolean);
      const pattern = travelDayNames.length > 0 ? travelDayNames.join(', ') : 'No pattern set';

      return {
        operator: assignment.operatorContract?.operator ? {
          name: assignment.operatorContract.operator.name,
          phone: assignment.operatorContract.operator.phone
        } : null,
        termPeriod: assignment.termPeriod,
        pickupLocation: formattedPickupLocation,
        dropoffLocation: formattedDropoffLocation,
        routeCode: assignment.operatorContract?.code || null,
        maxTravelTimeMins: assignment.maxTravelTimeMins,
        routeMetrics: assignment.routeMetrics[0] || null,
        // ADD: Include travel pattern and days
        pattern: pattern,
        travelDays: travelDayNames
      };
    };

    const response = {
      morning: formatTravelRun(morningRun),
      afternoon: formatTravelRun(afternoonRun),
      // ADD: Include budget/code information at top level
      budgetCode: currentCodeAssignment?.budget?.code || null,
      eligibilityCode: currentCodeAssignment?.eligibility?.code || null,
      fundingCode: currentCodeAssignment?.funding?.code || null,
      codeAssignment: currentCodeAssignment ? {
        validFrom: currentCodeAssignment.validFrom,
        validTo: currentCodeAssignment.validTo,
        budget: currentCodeAssignment.budget,
        eligibility: currentCodeAssignment.eligibility,
        funding: currentCodeAssignment.funding
      } : null
    };

    res.json({
      success: true,
      data: response
    });

  } catch (error) {
    console.error('Error fetching travel details:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch travel details',
      error: error.message
    });
  }
};

const getActualTravelData = async (req, res) => {
  try {
    const { studentId } = req.params;
    const studentIdInt = parseInt(studentId);

    if (isNaN(studentIdInt)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid student ID'
      });
    }

    const currentDate = new Date();
    
    // Get travel assignments for current term period only
    const travelAssignments = await prisma.studentTravelAssignment.findMany({
      where: {
        studentId: studentIdInt,
        termPeriod: {
          validFrom: { lte: currentDate },
          validTo: { gte: currentDate }
        }
      },
      select: {
        id: true,
        termId: true
      }
    });

    const assignmentIds = travelAssignments.map(assignment => assignment.id);
    const termPeriodIds = [...new Set(travelAssignments.map(assignment => assignment.termId))];

    if (assignmentIds.length === 0) {
      return res.json({
        success: true,
        data: {
          travelLogs: [],
          termExceptions: []
        }
      });
    }

    // Get travel logs for these assignments
    const travelLogs = await prisma.studentTravelLog.findMany({
      where: {
        assignmentId: {
          in: assignmentIds
        }
      },
      include: {
        assignment: {
          include: {
            operatorContract: {
              include: {
                operator: true
              }
            }
          }
        }
      },
      orderBy: {
        journeyDate: 'desc'
      },
      take: 100
    });

    // Get term exceptions for the term periods
    const termExceptions = await prisma.termException.findMany({
      where: {
        termId: {
          in: termPeriodIds
        }
      },
      include: {
        termPeriod: true
      },
      orderBy: {
        startDate: 'desc'
      }
    });

    const formattedLogs = travelLogs.map(log => ({
      journeyDate: log.journeyDate,
      runType: log.runType,
      status: log.status,
      pickupTimeOverride: log.pickupTimeOverride,
      dropoffTimeOverride: log.dropoffTimeOverride,
      notes: log.notes,
      operator: log.assignment?.operatorContract?.operator?.name || 'N/A',
      operatorPhone: log.assignment?.operatorContract?.operator?.phone || null,
      routeCode: log.assignment?.operatorContract?.code || null,
      createdAt: log.createdAt,
      updatedAt: log.updatedAt
    }));

    const formattedExceptions = termExceptions.map(exception => ({
      id: exception.id,
      termId: exception.termId,
      exceptionType: exception.exceptionType,
      startDate: exception.startDate,
      endDate: exception.endDate,
      description: exception.description,
      termPeriod: {
        id: exception.termPeriod.id,
        name: exception.termPeriod.name,
        validFrom: exception.termPeriod.validFrom,
        validTo: exception.termPeriod.validTo
      },
      createdAt: exception.createdAt,
      updatedAt: exception.updatedAt
    }));

    res.json({
      success: true,
      data: {
        travelLogs: formattedLogs,
        termExceptions: formattedExceptions
      }
    });

  } catch (error) {
    console.error('Error fetching actual travel data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch actual travel data',
      error: error.message
    });
  }
};

const updateTravelStatusForStudent = async (req, res) => {
  try {
    const { studentId } = req.params;
    const { journeyDate, runType, status } = req.body;
    
    const studentIdInt = parseInt(studentId);

    // Validate input
    if (isNaN(studentIdInt)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid student ID'
      });
    }

    if (!journeyDate || !runType || !status) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: journeyDate, runType, and status are required'
      });
    }

    // Validate runType
    if (!['AM', 'PM'].includes(runType)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid runType. Must be either AM or PM'
      });
    }

    // Validate status (removed "Not Set" as per frontend changes)
    const validStatuses = ['Confirmed', 'Missed', 'Holiday', 'Student Ill'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: `Invalid status. Must be one of: ${validStatuses.join(', ')}`
      });
    }

    // Parse and validate journey date
    const parsedJourneyDate = new Date(journeyDate);
    if (isNaN(parsedJourneyDate.getTime())) {
      return res.status(400).json({
        success: false,
        message: 'Invalid journeyDate format'
      });
    }

    // Get student's travel assignments to find the correct assignment IDs
    const travelAssignments = await prisma.studentTravelAssignment.findMany({
      where: {
        studentId: studentIdInt,
        runType: runType
      },
      select: {
        id: true
      }
    });

    if (travelAssignments.length === 0) {
      return res.status(404).json({
        success: false,
        message: `No travel assignment found for student ${studentId} with runType ${runType}`
      });
    }

    const assignmentIds = travelAssignments.map(assignment => assignment.id);

    // Find existing travel log entry
    const existingLog = await prisma.studentTravelLog.findFirst({
      where: {
        assignmentId: {
          in: assignmentIds
        },
        journeyDate: parsedJourneyDate,
        runType: runType
      }
    });

    let result;

    if (status === 'Confirmed') {
      // If status is Confirmed, delete the existing log (if it exists)
      if (existingLog) {
        await prisma.studentTravelLog.delete({
          where: {
            id: existingLog.id
          }
        });
        
        result = {
          action: 'deleted',
          message: 'Travel log deleted (status set to Confirmed)',
          journeyDate: parsedJourneyDate,
          runType: runType,
          status: 'Confirmed'
        };
      } else {
        // No existing log, nothing to delete
        result = {
          action: 'no_change',
          message: 'No travel log exists, status remains Confirmed by default',
          journeyDate: parsedJourneyDate,
          runType: runType,
          status: 'Confirmed'
        };
      }
    } else {
      // For non-Confirmed statuses, update existing or create new log
      if (existingLog) {
        // Update existing log
        const updatedLog = await prisma.studentTravelLog.update({
          where: {
            id: existingLog.id
          },
          data: {
            status: status,
            updatedAt: new Date()
          },
          include: {
            assignment: {
              include: {
                operatorContract: {
                  include: {
                    operator: true
                  }
                }
              }
            }
          }
        });

        result = {
          action: 'updated',
          message: 'Travel status updated successfully',
          data: {
            journeyDate: updatedLog.journeyDate,
            runType: updatedLog.runType,
            status: updatedLog.status,
            pickupTimeOverride: updatedLog.pickupTimeOverride,
            dropoffTimeOverride: updatedLog.dropoffTimeOverride,
            notes: updatedLog.notes,
            operator: updatedLog.assignment?.operatorContract?.operator?.name || 'N/A',
            operatorPhone: updatedLog.assignment?.operatorContract?.operator?.phone || null,
            routeCode: updatedLog.assignment?.operatorContract?.code || null,
            createdAt: updatedLog.createdAt,
            updatedAt: updatedLog.updatedAt
          }
        };
      } else {
        // Create new log entry
        const newLog = await prisma.studentTravelLog.create({
          data: {
            assignmentId: assignmentIds[0], // Use first assignment ID
            journeyDate: parsedJourneyDate,
            runType: runType,
            status: status,
            createdAt: new Date(),
            updatedAt: new Date()
          },
          include: {
            assignment: {
              include: {
                operatorContract: {
                  include: {
                    operator: true
                  }
                }
              }
            }
          }
        });

        result = {
          action: 'created',
          message: 'Travel log created successfully',
          data: {
            journeyDate: newLog.journeyDate,
            runType: newLog.runType,
            status: newLog.status,
            pickupTimeOverride: newLog.pickupTimeOverride,
            dropoffTimeOverride: newLog.dropoffTimeOverride,
            notes: newLog.notes,
            operator: newLog.assignment?.operatorContract?.operator?.name || 'N/A',
            operatorPhone: newLog.assignment?.operatorContract?.operator?.phone || null,
            routeCode: newLog.assignment?.operatorContract?.code || null,
            createdAt: newLog.createdAt,
            updatedAt: newLog.updatedAt
          }
        };
      }
    }

    res.json({
      success: true,
      ...result
    });

  } catch (error) {
    console.error('Error updating travel status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update travel status',
      error: error.message
    });
  }
};

module.exports = {
  getTravelDetails,
  getActualTravelData,
  updateTravelStatusForStudent
};